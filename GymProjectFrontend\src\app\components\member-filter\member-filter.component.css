/* Member Filter Component Styles */

/* Content Blur Effect */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* View Toggle Container */
.view-toggle-container {
  display: flex;
  align-items: center;
}

.view-toggle-container .modern-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

/* Çoklu Branş Sistemi Stilleri */
.branch-summary-container {
  margin-top: 1rem;
}

.branch-cards {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.branch-summary-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.branch-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--primary);
}

.branch-card-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.branch-name {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  font-size: 0.9rem;
}

.member-count-badge {
  background: var(--primary);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.branch-card-body {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.branch-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.branch-stat i {
  width: 16px;
  text-align: center;
}

/* Paket Detayları */
.package-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.package-details-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.package-cards {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.package-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.package-card:hover {
  border-color: var(--primary);
  background: var(--hover-bg);
}

.package-card.selected {
  border-color: var(--primary);
  background: var(--primary-light);
}

.package-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.package-name {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  font-size: 0.85rem;
}

.package-price {
  background: var(--success);
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  font-weight: 600;
}

.package-card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-stat {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.package-stat i {
  width: 12px;
  text-align: center;
}

/* Üye Bilgileri Geliştirmeleri */
.member-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.member-name-text {
  font-weight: 500;
  color: var(--text-primary);
}

.member-balance {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Ödeme Durumu */
.payment-status {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-start;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.payment-completed {
  background: var(--success-light);
  color: var(--success);
}

.payment-pending {
  background: var(--warning-light);
  color: var(--warning);
}

.payment-failed {
  background: var(--danger-light);
  color: var(--danger);
}

.payment-expiring {
  background: var(--danger-light);
  color: var(--danger);
  animation: pulse 2s infinite;
}

.payment-warning {
  background: var(--warning-light);
  color: var(--warning);
}

.payment-active {
  background: var(--info-light);
  color: var(--info);
}

.payment-amount {
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Risk Seviyeleri */
.risk-high {
  background: var(--danger-light);
  color: var(--danger);
  border: 1px solid var(--danger);
}

.risk-medium {
  background: var(--warning-light);
  color: var(--warning);
  border: 1px solid var(--warning);
}

.risk-low {
  background: var(--success-light);
  color: var(--success);
  border: 1px solid var(--success);
}

.risk-unknown {
  background: var(--secondary-light);
  color: var(--secondary);
  border: 1px solid var(--secondary);
}

/* Aksiyon Butonları Geliştirmeleri */
.action-buttons {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.action-buttons .modern-btn {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.action-buttons .modern-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Animasyonlar */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive Tasarım */
@media (max-width: 768px) {
  .branch-cards {
    gap: 0.5rem;
  }

  .branch-summary-card {
    padding: 0.75rem;
  }

  .package-cards {
    gap: 0.375rem;
  }

  .package-card {
    padding: 0.5rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.125rem;
  }

  .action-buttons .modern-btn {
    width: 100%;
    padding: 0.25rem;
  }
}

/* Dark Mode Uyumluluğu */
[data-theme="dark"] .branch-summary-card {
  background: var(--dark-card-bg);
  border-color: var(--dark-border-color);
}

[data-theme="dark"] .branch-summary-card:hover {
  border-color: var(--primary);
  background: var(--dark-hover-bg);
}

[data-theme="dark"] .package-card {
  background: var(--dark-card-bg);
  border-color: var(--dark-border-color);
}

[data-theme="dark"] .package-card:hover {
  background: var(--dark-hover-bg);
}

[data-theme="dark"] .package-card.selected {
  background: var(--primary-dark);
}

/* Filter Card Styles */
.filter-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.filter-card:hover {
  transform: translateY(-5px);
}

.filter-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.filter-title {
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

/* Modern Radio Buttons */
.modern-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modern-radio {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.95rem;
  margin-bottom: 0;
  transition: all 0.2s ease;
}

.radio-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 2px solid var(--secondary);
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.2s ease;
}

.radio-icon:after {
  content: '';
  position: absolute;
  display: none;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon {
  border-color: var(--primary);
}

.modern-radio-input:checked ~ .modern-radio-label .radio-icon:after {
  display: block;
}

.modern-radio-input:focus ~ .modern-radio-label .radio-icon {
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-radio-label:hover {
  color: var(--primary);
}

/* Member List Card */
.member-list-card {
  height: 100%;
}

/* Modern Search Input */
.search-container {
  position: relative;
  width: 300px;
}

.modern-search-input {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search-input input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  border-radius: var(--border-radius-md);
  background-color: rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.modern-search-input input:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--secondary);
  font-size: 0.95rem;
  pointer-events: none;
}

/* Table Container */
.table-container {
  overflow-x: auto;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-md);
}

/* Member Name with Avatar */
.member-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Remaining Days Styling */
.remaining-days {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

/* Pagination Container */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

/* Gender Chart Card */
.gender-chart-card {
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.gender-chart-card:hover {
  transform: translateY(-5px);
}

.gender-chart-card .modern-card-body {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 220px;
}

/* Dark Mode Support */
[data-theme="dark"] .filter-section {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-search-input input {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-search-input input:focus {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .gender-chart-container {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-card {
  background-color: #2d3748;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-card-header {
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table th {
  background-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-table td {
  color: #e2e8f0;
  border-bottom-color: #4a5568;
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modern-pagination .modern-page-link {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

[data-theme="dark"] .modern-pagination .modern-page-link:hover {
  background-color: #4a5568;
}

[data-theme="dark"] .modern-pagination .modern-page-item.disabled .modern-page-link {
  background-color: #2d3748;
  color: #718096;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .search-container {
    width: 100%;
    margin-top: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    width: 100%;
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
  
  .action-buttons button:first-child {
    margin-top: 0;
  }
  
  .member-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
  