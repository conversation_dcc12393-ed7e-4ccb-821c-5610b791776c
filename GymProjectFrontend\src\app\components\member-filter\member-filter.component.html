<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Filtreler ve İstatistikler -->
    <div class="col-md-3">
      <div class="modern-card filter-card slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filtreler
          </h5>

          <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Değiştirme Butonu -->
          <div class="view-toggle-container">
            <button
              class="modern-btn modern-btn-sm"
              [class.modern-btn-primary]="isMultiBranchMode"
              [class.modern-btn-secondary]="!isMultiBranchMode"
              (click)="toggleViewMode()"
              title="Görünüm <PERSON>"
            >
              <i class="fas" [class.fa-layer-group]="isMultiBranchMode" [class.fa-list]="!isMultiBranchMode"></i>
              {{ isMultiBranchMode ? 'Çoklu Branş' : 'Klasik' }}
            </button>
          </div>
        </div>
        
        <div class="modern-card-body">
          <!-- Cinsiyet Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-venus-mars me-2"></i>
              Cinsiyet Filtreleri
            </h6>
            
            <div class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-all"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  [value]="''"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-all">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-male"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="1"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-male">
                  <span class="radio-icon"></span>
                  <i class="fas fa-male text-primary me-1"></i>
                  Erkek
                  <span class="modern-badge modern-badge-primary ms-2">{{ genderCounts.male }}</span>
                </label>
              </div>
              
              <div class="modern-radio">
                <input
                  type="radio"
                  id="gender-female"
                  class="modern-radio-input"
                  name="genderFilter"
                  [(ngModel)]="genderFilter"
                  value="2"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="gender-female">
                  <span class="radio-icon"></span>
                  <i class="fas fa-female text-danger me-1"></i>
                  Kadın
                  <span class="modern-badge modern-badge-danger ms-2">{{ genderCounts.female }}</span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Branş Filtreleri -->
          <div class="filter-section">
            <h6 class="filter-title">
              <i class="fas fa-dumbbell me-2"></i>
              {{ isMultiBranchMode ? 'Branş Özeti' : 'Branş Filtreleri' }}
            </h6>

            <!-- Çoklu Branş Modu -->
            <div *ngIf="isMultiBranchMode" class="branch-summary-container">
              <!-- Genel Görünüm Butonu -->
              <button
                *ngIf="filterState.isDetailView"
                class="modern-btn modern-btn-outline-secondary modern-btn-sm mb-3 w-100"
                (click)="backToBranchSummary()"
              >
                <i class="fas fa-arrow-left me-2"></i>
                Genel Görünüme Dön
              </button>

              <!-- Branş Özet Kartları -->
              <div *ngIf="!filterState.isDetailView && !isBranchSummaryLoading" class="branch-cards">
                <div
                  *ngFor="let branch of branchSummaries"
                  class="branch-summary-card"
                  (click)="selectBranch(branch.branch)"
                >
                  <div class="branch-card-header">
                    <h6 class="branch-name">{{ branch.branch }}</h6>
                    <span class="member-count-badge">{{ branch.memberCount }}</span>
                  </div>
                  <div class="branch-card-body">
                    <div class="branch-stat">
                      <i class="fas fa-calendar-day text-primary"></i>
                      <span>{{ branch.totalRemainingDays }} toplam gün</span>
                    </div>
                    <div class="branch-stat">
                      <i class="fas fa-chart-line text-success"></i>
                      <span>{{ branch.avgRemainingDays }} ort. gün</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Branş Yükleniyor -->
              <div *ngIf="isBranchSummaryLoading" class="text-center py-3">
                <i class="fas fa-spinner fa-spin"></i>
                <p class="mt-2 mb-0">Branş özeti yükleniyor...</p>
              </div>

              <!-- Paket Detayları -->
              <div *ngIf="showPackageDetails && !isPackageDetailsLoading" class="package-details">
                <h6 class="package-details-title">
                  <i class="fas fa-box me-2"></i>
                  {{ filterState.selectedBranch }} - Paket Türleri
                </h6>

                <div class="package-cards">
                  <div
                    *ngFor="let package of selectedBranchPackages"
                    class="package-card"
                    [class.selected]="filterState.selectedPackageType === package.membershipTypeID"
                    (click)="selectPackageType(package.membershipTypeID)"
                  >
                    <div class="package-card-header">
                      <h6 class="package-name">{{ package.typeName }}</h6>
                      <span class="package-price">{{ formatPrice(package.price) }}</span>
                    </div>
                    <div class="package-card-body">
                      <div class="package-stat">
                        <i class="fas fa-users text-info"></i>
                        <span>{{ package.memberCount }} üye</span>
                      </div>
                      <div class="package-stat">
                        <i class="fas fa-clock text-warning"></i>
                        <span>{{ package.day }} gün</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Paket Detayları Yükleniyor -->
              <div *ngIf="isPackageDetailsLoading" class="text-center py-3">
                <i class="fas fa-spinner fa-spin"></i>
                <p class="mt-2 mb-0">Paket detayları yükleniyor...</p>
              </div>
            </div>

            <!-- Klasik Mod -->
            <div *ngIf="!isMultiBranchMode" class="modern-radio-group">
              <div class="modern-radio">
                <input
                  type="radio"
                  id="branch-all"
                  class="modern-radio-input"
                  name="branchFilter"
                  [(ngModel)]="branchFilter"
                  [value]="''"
                  (change)="onFilterChange()"
                />
                <label class="modern-radio-label" for="branch-all">
                  <span class="radio-icon"></span>
                  Tümü
                </label>
              </div>

              <ng-container *ngFor="let type of membershipTypes; let i = index">
                <div class="modern-radio" *ngIf="branchCounts[type.branch] > 0">
                  <input
                    type="radio"
                    [id]="'branch-' + i"
                    class="modern-radio-input"
                    name="branchFilter"
                    [(ngModel)]="branchFilter"
                    [value]="type.branch"
                    (change)="onFilterChange()"
                  />
                  <label class="modern-radio-label" [for]="'branch-' + i">
                    <span class="radio-icon"></span>
                    {{ type.branch }}
                    <span class="modern-badge modern-badge-info ms-2">{{ branchCounts[type.branch] }}</span>
                  </label>
                </div>
              </ng-container>
            </div>
          </div>
          
          <!-- Üye İstatistikleri -->
          <div class="modern-stats-card mt-4">
            <div class="modern-stats-icon bg-primary">
              <i class="fas fa-users"></i>
            </div>
            <div class="modern-stats-info">
              <h2 class="modern-stats-value">{{ totalActiveMembers }}</h2>
              <p class="modern-stats-label">Toplam Aktif Üye</p>
            </div>
          </div>
          
        </div>
      </div>
      
      <!-- Cinsiyet Dağılımı Grafiği -->
      <div class="modern-card gender-chart-card mt-3 slide-in-left">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-chart-pie me-2"></i>
            Cinsiyet Dağılımı
          </h5>
        </div>
        <div class="modern-card-body">
          <canvas id="genderChart" width="100%" height="180"></canvas>
        </div>
      </div>
    </div>

    <!-- Üye Listesi -->
    <div class="col-md-9">
      <div class="modern-card member-list-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            {{ isMultiBranchMode ? 'Çoklu Branş Üye Listesi' : 'Aktif Üye Listesi' }}
            <span *ngIf="filterState.selectedBranch" class="ms-2">
              <span class="modern-badge modern-badge-primary">{{ filterState.selectedBranch }}</span>
            </span>
          </h5>

          <!-- Arama Kutusu -->
          <div class="search-container">
            <div class="modern-search-input">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                [ngModel]="memberFilterText"
                (ngModelChange)="searchTextChanged($event)"
                placeholder="Ad, Soyad veya Telefon"
              />
            </div>
          </div>
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-user me-2"></i>
                    Ad Soyad
                  </th>
                  <th>
                    <i class="fas fa-phone me-2"></i>
                    Telefon
                  </th>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş
                  </th>
                  <th *ngIf="isMultiBranchMode">
                    <i class="fas fa-tags me-2"></i>
                    Paket Türü
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Kalan Gün
                  </th>
                  <th *ngIf="isMultiBranchMode">
                    <i class="fas fa-credit-card me-2"></i>
                    Durum
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- Çoklu Branş Modu -->
                <ng-container *ngIf="isMultiBranchMode">
                  <tr *ngFor="let member of multiBranchMembers" class="zoom-in">
                    <td>
                      <div class="member-name">
                        <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                          {{ member.name.charAt(0) }}
                        </div>
                        <div class="member-info">
                          <span class="member-name-text">{{ member.name }}</span>
                          <small *ngIf="member.balance > 0" class="member-balance text-success">
                            <i class="fas fa-wallet me-1"></i>
                            {{ formatPrice(member.balance) }}
                          </small>
                        </div>
                      </div>
                    </td>
                    <td>{{ member.phoneNumber }}</td>
                    <td>
                      <span class="modern-badge modern-badge-info">{{ member.branch }}</span>
                      <span *ngIf="member.isFrozen" class="modern-badge modern-badge-warning ms-1">
                        <i class="fas fa-snowflake me-1"></i>Donmuş
                      </span>
                    </td>
                    <td>
                      <span class="modern-badge modern-badge-secondary">{{ member.typeName }}</span>
                    </td>
                    <td>
                      <div class="remaining-days">
                        <span [ngClass]="getRemainingDaysClass(member.remainingDays)">
                          {{ member.remainingDays }} gün
                        </span>
                        <span *ngIf="member.isFutureStartDate" class="modern-badge modern-badge-warning ms-2">
                          Başlangıç: {{ formatDate(member.startDate) }}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div class="payment-status">
                        <span class="status-badge" [ngClass]="getPaymentStatusClass(member.paymentStatus)">
                          {{ member.paymentStatus }}
                        </span>
                        <small *ngIf="member.lastPaymentAmount" class="payment-amount">
                          {{ formatPrice(member.lastPaymentAmount) }}
                        </small>
                      </div>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button
                          class="modern-btn modern-btn-danger modern-btn-sm"
                          (click)="openSmartDeleteDialog(member)"
                          title="Akıllı Silme"
                        >
                          <i class="fas fa-trash-alt"></i>
                        </button>
                        <button
                          class="modern-btn modern-btn-info modern-btn-sm ms-1"
                          (click)="openFreezeDialog(member)"
                          [disabled]="member.remainingDays <= 0 || member.isFrozen"
                          title="Üyeliği Dondur"
                        >
                          <i class="fas fa-snowflake"></i>
                        </button>
                        <button
                          class="modern-btn modern-btn-success modern-btn-sm ms-1"
                          (click)="openRenewalDialog(member)"
                          title="Akıllı Yenileme"
                        >
                          <i class="fas fa-sync-alt"></i>
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Çoklu branş - Veri yoksa gösterilecek mesaj -->
                  <tr *ngIf="multiBranchMembers.length === 0">
                    <td [attr.colspan]="isMultiBranchMode ? 7 : 5" class="text-center py-4">
                      <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                      <p class="mb-0">Arama kriterlerine uygun üye bulunamadı.</p>
                    </td>
                  </tr>
                </ng-container>

                <!-- Klasik Mod -->
                <ng-container *ngIf="!isMultiBranchMode">
                  <tr *ngFor="let member of members" class="zoom-in">
                    <td>
                      <div class="member-name">
                        <div class="modern-avatar" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
                          {{ member.name.charAt(0) }}
                        </div>
                        <span>{{ member.name }}</span>
                      </div>
                    </td>
                    <td>{{ member.phoneNumber }}</td>
                    <td>
                      <span class="modern-badge modern-badge-info">{{ member.branch }}</span>
                    </td>
                    <td>
                      <div class="remaining-days">
                        <span [ngClass]="{'text-success': member.remainingDays > 10, 'text-warning': member.remainingDays <= 10 && member.remainingDays > 3, 'text-danger': member.remainingDays <= 3}">
                          {{ member.remainingDays }} gün
                        </span>
                        <span *ngIf="member.isFutureStartDate" class="modern-badge modern-badge-warning ms-2">
                          Başlangıç: {{ member.startDate | date:'dd.MM.yyyy' }}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div class="action-buttons">
                        <button
                          class="modern-btn modern-btn-danger modern-btn-sm"
                          (click)="deleteMember(member)"
                          title="Üyeyi Sil"
                        >
                          <i class="fas fa-trash-alt"></i>
                        </button>
                        <button
                          class="modern-btn modern-btn-info modern-btn-sm ms-2"
                          (click)="openFreezeDialog(member)"
                          [disabled]="member.remainingDays <= 0"
                          title="Üyeliği Dondur"
                        >
                          <i class="fas fa-snowflake"></i>
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Klasik mod - Veri yoksa gösterilecek mesaj -->
                  <tr *ngIf="members.length === 0">
                    <td colspan="5" class="text-center py-4">
                      <i class="fas fa-search fa-2x mb-2 text-muted"></i>
                      <p class="mb-0">Arama kriterlerine uygun üye bulunamadı.</p>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
          
          <!-- Sayfalama -->
          <div class="pagination-container" *ngIf="totalPages > 1">
            <ul class="modern-pagination">
              <li class="modern-page-item" [class.disabled]="currentPage === 1">
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage - 1)"
                >
                  <i class="fas fa-chevron-left"></i>
                </a>
              </li>
              <li
                class="modern-page-item"
                *ngFor="let i of [].constructor(totalPages); let idx = index"
                [class.active]="currentPage === idx + 1"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(idx + 1)"
                >
                  {{ idx + 1 }}
                </a>
              </li>
              <li
                class="modern-page-item"
                [class.disabled]="currentPage === totalPages"
              >
                <a
                  class="modern-page-link"
                  href="javascript:void(0)"
                  (click)="onPageChange(currentPage + 1)"
                >
                  <i class="fas fa-chevron-right"></i>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
