// Çoklu branş üyelik sistemi için model tanımları

export interface MultiBranchMemberFilter {
  memberID: number;
  membershipID: number;
  membershipTypeID: number;
  name: string;
  gender: number;
  phoneNumber: string;
  branch: string;
  typeName: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
  isActive: boolean;
  isFutureStartDate: boolean;
  updatedDate?: string;
  balance: number;
  isFrozen: boolean;
  lastPaymentAmount?: number;
  paymentStatus: string;
}

export interface BranchSummary {
  branch: string;
  memberCount: number;
  totalRemainingDays: number;
  avgRemainingDays: number;
  packageTypes: PackageTypeCount[];
}

export interface PackageTypeCount {
  membershipTypeID: number;
  typeName: string;
  day: number;
  price: number;
  memberCount: number;
  totalRemainingDays: number;
}

export interface MemberActiveMemberships {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  activeMemberships: ActiveMembershipDetail[];
  totalActiveMemberships: number;
  totalRemainingDays: number;
}

export interface ActiveMembershipDetail {
  membershipID: number;
  branch: string;
  typeName: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
  lastPaymentAmount?: number;
  paymentStatus: string;
  deletionRiskLevel: string;
  deletionWarning: string;
}

// Akıllı üyelik yenileme modelleri
export interface SmartMembershipRenewal {
  memberID: number;
  membershipTypeID: number;
  startDate: string;
  endDate: string;
  price: number;
  paymentMethod: string;
  day: number;
  renewalStrategy: string; // EXTEND, NEW, UPGRADE
  existingMembershipID?: number;
  requiresUserConfirmation: boolean;
  confirmationMessage?: string;
}

export interface MembershipRenewalAnalysis {
  memberID: number;
  memberName: string;
  requestedMembershipTypeID: number;
  requestedBranch: string;
  requestedTypeName: string;
  existingMemberships: ExistingMembership[];
  recommendedAction: RecommendedAction;
  alternativeActions: AlternativeAction[];
}

export interface ExistingMembership {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
  lastPaymentAmount: number;
  isSameBranch: boolean;
  isSamePackageType: boolean;
}

export interface RecommendedAction {
  actionType: string;
  actionDescription: string;
  reason: string;
  isOptimal: boolean;
  resultPreview: ActionResultPreview;
}

export interface AlternativeAction {
  actionType: string;
  actionDescription: string;
  pros: string;
  cons: string;
  resultPreview: ActionResultPreview;
}

export interface ActionResultPreview {
  totalActiveMemberships: number;
  totalRemainingDays: number;
  activeBranches: string[];
  estimatedMonthlyCost: number;
}

// Akıllı silme modelleri
export interface SmartMembershipDeletionRequest {
  memberID: number;
  membershipIDsToDelete: number[];
  deletionType: string; // SINGLE, MULTIPLE, ALL
  userConfirmed: boolean;
  deletionReason?: string;
}

export interface MembershipDeletionAnalysis {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  deletableMemberships: DeletableMembership[];
  totalActiveMemberships: number;
  totalRiskAmount: number;
  recommendedStrategy: RecommendedDeletionStrategy;
  warnings: string[];
}

export interface DeletableMembership {
  membershipID: number;
  branch: string;
  typeName: string;
  startDate: string;
  endDate: string;
  remainingDays: number;
  lastPaymentAmount: number;
  lastPaymentDate: string;
  paymentStatus: string;
  riskLevel: string; // HIGH, MEDIUM, LOW
  riskAmount: number;
  riskDescription: string;
  canBeDeleted: boolean;
  deletionBlockReason?: string;
  recommendedAction: string; // DELETE, FREEZE, TRANSFER
  recommendedActionDescription: string;
}

export interface RecommendedDeletionStrategy {
  strategyType: string; // SELECTIVE, COMPLETE, FREEZE, TRANSFER
  strategyDescription: string;
  reasoning: string;
  membershipIDsToDelete: number[];
  membershipIDsToKeep: number[];
  estimatedFinancialImpact: number;
  customerSatisfactionRisk: number; // 1-10 arası
}

export interface MembershipDeletionResult {
  isSuccess: boolean;
  message: string;
  deletedMembershipIDs: number[];
  failedDeletions: { [key: number]: string };
  postDeletionStatus: PostDeletionMemberStatus;
  summary: DeletionSummary;
}

export interface PostDeletionMemberStatus {
  memberID: number;
  memberName: string;
  remainingActiveMemberships: number;
  remainingBranches: string[];
  totalRemainingDays: number;
  isCompletelyInactive: boolean;
  nextExpirationDate?: string;
}

export interface DeletionSummary {
  totalMembershipsDeleted: number;
  totalRefundAmount: number;
  deletedBranches: string[];
  deletionDate: string;
  deletionReason?: string;
  financialImpact: number;
  refundRecordIDs: number[];
}

// Yardımcı modeller
export interface MemberCurrentStatus {
  memberID: number;
  name: string;
  totalActiveMemberships: number;
  totalRemainingDays: number;
  activeBranches: string[];
  lastActivityDate: string;
  totalBalance: number;
}

export interface BranchStatistics {
  [key: string]: any;
}

export interface PerformanceMetrics {
  totalActiveMembers: number;
  totalActiveMemberships: number;
  cacheHitRate: string;
  avgResponseTime: string;
}

// API Response modelleri
export interface MultiBranchApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  totalCount?: number;
}

export interface PaginatedMultiBranchResponse<T> {
  success: boolean;
  message: string;
  data: {
    data: T[];
    totalPages: number;
    totalCount: number;
    currentPage: number;
    pageSize: number;
  };
}

// Filtreleme parametreleri
export interface MultiBranchFilterParams {
  searchText?: string;
  gender?: number;
  branchFilter?: string;
  pageNumber?: number;
  pageSize?: number;
}

// İki seviyeli filtreleme için state modeli
export interface TwoLevelFilterState {
  selectedBranch?: string;
  selectedPackageType?: number;
  isDetailView: boolean;
  branchSummaries: BranchSummary[];
  filteredMembers: MultiBranchMemberFilter[];
}

// Akıllı işlem durumları
export enum RenewalStrategy {
  EXTEND = 'EXTEND',
  NEW = 'NEW',
  UPGRADE = 'UPGRADE'
}

export enum DeletionType {
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
  ALL = 'ALL'
}

export enum RiskLevel {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW'
}

export enum PaymentStatus {
  COMPLETED = 'COMPLETED',
  PENDING = 'PENDING',
  FAILED = 'FAILED',
  ACTIVE = 'ACTIVE',
  EXPIRING_SOON = 'EXPIRING_SOON',
  WARNING = 'WARNING'
}
