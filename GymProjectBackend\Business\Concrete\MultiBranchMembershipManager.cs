using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    /// <summary>
    /// Çoklu branş üyelik yönetimi manager sınıfı
    /// Ak<PERSON><PERSON><PERSON> yenileme, gü<PERSON>li silme ve iki seviyeli filtreleme işlemlerini yönetir
    /// </summary>
    public class MultiBranchMembershipManager : IMultiBranchMembershipService
    {
        private readonly IMembershipDal _membershipDal;
        private readonly IMemberDal _memberDal;
        private readonly IMembershiptypeDal _membershipTypeDal;
        private readonly IPaymentDal _paymentDal;
        private readonly ICompanyContext _companyContext;

        public MultiBranchMembershipManager(
            IMembershipDal membershipDal,
            IMemberDal memberDal,
            IMembershiptypeDal membershipTypeDal,
            IPaymentDal paymentDal,
            ICompanyContext companyContext)
        {
            _membershipDal = membershipDal;
            _memberDal = memberDal;
            _membershipTypeDal = membershipTypeDal;
            _paymentDal = paymentDal;
            _companyContext = companyContext;
        }

        #region Çoklu Branş Filtreleme İşlemleri

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 5, "MultiBranch", "MemberDetails")] // 5 dakika cache
        public IDataResult<List<MultiBranchMemberFilterDto>> GetMultiBranchMemberDetails(
            string? searchText = null,
            int? gender = null,
            string? branchFilter = null,
            int pageNumber = 1,
            int pageSize = 50)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Geçici olarak normal sorgu kullan, stored procedure daha sonra eklenecek
                var query = from m in _memberDal.GetAll(mem => mem.CompanyID == companyId && mem.IsActive == true)
                           join ms in _membershipDal.GetAll(membership =>
                               membership.CompanyID == companyId &&
                               membership.IsActive == true &&
                               membership.EndDate > DateTime.Now &&
                               membership.IsFrozen == false)
                           on m.MemberID equals ms.MemberID
                           join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId && type.IsActive == true)
                           on ms.MembershipTypeID equals mt.MembershipTypeID
                           where (string.IsNullOrEmpty(searchText) || m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText))
                              && (!gender.HasValue || m.Gender == gender.Value)
                              && (string.IsNullOrEmpty(branchFilter) || mt.Branch == branchFilter)
                           select new MultiBranchMemberFilterDto
                           {
                               MemberID = m.MemberID,
                               MembershipID = ms.MembershipID,
                               MembershipTypeID = ms.MembershipTypeID,
                               Name = m.Name,
                               Gender = m.Gender,
                               PhoneNumber = m.PhoneNumber,
                               Branch = mt.Branch,
                               TypeName = mt.TypeName,
                               StartDate = ms.StartDate,
                               EndDate = ms.EndDate,
                               RemainingDays = ms.StartDate > DateTime.Now ?
                                   (int)(ms.EndDate - ms.StartDate).TotalDays :
                                   (int)(ms.EndDate - DateTime.Now).TotalDays,
                               IsActive = ms.IsActive ?? true,
                               IsFutureStartDate = ms.StartDate > DateTime.Now,
                               UpdatedDate = ms.UpdatedDate ?? ms.CreationDate,
                               Balance = m.Balance,
                               IsFrozen = ms.IsFrozen
                           };

                var result = query.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

                // Her üye için ek bilgileri zenginleştir
                foreach (var member in result)
                {
                    EnrichMemberData(member);
                }

                return new SuccessDataResult<List<MultiBranchMemberFilterDto>>(result, Messages.MembersListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<MultiBranchMemberFilterDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        [MultiTenantCacheAspect(duration: 10, "MultiBranch", "BranchSummary")] // 10 dakika cache
        public IDataResult<List<BranchSummaryDto>> GetBranchSummary()
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Geçici olarak normal sorgu kullan
                var branchData = from ms in _membershipDal.GetAll(membership =>
                                    membership.CompanyID == companyId &&
                                    membership.IsActive == true &&
                                    membership.EndDate > DateTime.Now)
                                join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId)
                                on ms.MembershipTypeID equals mt.MembershipTypeID
                                join m in _memberDal.GetAll(member => member.CompanyID == companyId && member.IsActive == true)
                                on ms.MemberID equals m.MemberID
                                group new { ms, mt, m } by mt.Branch into g
                                select new BranchSummaryDto
                                {
                                    Branch = g.Key,
                                    MemberCount = g.Select(x => x.m.MemberID).Distinct().Count(),
                                    TotalRemainingDays = g.Sum(x => x.ms.EndDate > DateTime.Now ?
                                        (int)(x.ms.EndDate - DateTime.Now).TotalDays : 0),
                                    AvgRemainingDays = (int)g.Average(x => x.ms.EndDate > DateTime.Now ?
                                        (x.ms.EndDate - DateTime.Now).TotalDays : 0)
                                };

                var result = branchData.ToList();

                // Her branş için paket türlerini ekle
                foreach (var branch in result)
                {
                    branch.PackageTypes = GetPackageTypesByBranch(branch.Branch).Data ?? new List<PackageTypeCountDto>();
                }

                return new SuccessDataResult<List<BranchSummaryDto>>(result, Messages.BranchSummaryListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<BranchSummaryDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        public IDataResult<List<PackageTypeCountDto>> GetPackageTypesByBranch(string branch)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Belirli branştaki paket türlerini ve üye sayılarını getir
                var packageTypes = _membershipTypeDal.GetAll(mt => 
                    mt.CompanyID == companyId && 
                    mt.Branch == branch && 
                    mt.IsActive == true);

                var result = new List<PackageTypeCountDto>();

                foreach (var packageType in packageTypes)
                {
                    var memberCount = _membershipDal.GetAll(ms => 
                        ms.CompanyID == companyId &&
                        ms.MembershipTypeID == packageType.MembershipTypeID &&
                        ms.IsActive == true &&
                        ms.EndDate > DateTime.Now).Count;

                    var totalRemainingDays = _membershipDal.GetAll(ms => 
                        ms.CompanyID == companyId &&
                        ms.MembershipTypeID == packageType.MembershipTypeID &&
                        ms.IsActive == true &&
                        ms.EndDate > DateTime.Now)
                        .Sum(ms => (int)(ms.EndDate - DateTime.Now).TotalDays);

                    result.Add(new PackageTypeCountDto
                    {
                        MembershipTypeID = packageType.MembershipTypeID,
                        TypeName = packageType.TypeName,
                        Day = packageType.Day,
                        Price = packageType.Price,
                        MemberCount = memberCount,
                        TotalRemainingDays = totalRemainingDays
                    });
                }

                return new SuccessDataResult<List<PackageTypeCountDto>>(result, Messages.PackageTypesListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<PackageTypeCountDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        public IDataResult<MemberActiveMembershipsDto> GetMemberActiveMemberships(int memberID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var member = _memberDal.Get(m => m.MemberID == memberID && m.CompanyID == companyId);
                if (member == null)
                {
                    return new ErrorDataResult<MemberActiveMembershipsDto>(Messages.MemberNotFound);
                }

                // Geçici olarak normal sorgu kullan
                var activeMemberships = (from ms in _membershipDal.GetAll(membership =>
                                            membership.MemberID == memberID &&
                                            membership.CompanyID == companyId &&
                                            membership.IsActive == true &&
                                            membership.EndDate > DateTime.Now)
                                        join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId)
                                        on ms.MembershipTypeID equals mt.MembershipTypeID
                                        select new ActiveMembershipDetailDto
                                        {
                                            MembershipID = ms.MembershipID,
                                            Branch = mt.Branch,
                                            TypeName = mt.TypeName,
                                            StartDate = ms.StartDate,
                                            EndDate = ms.EndDate,
                                            RemainingDays = (int)(ms.EndDate - DateTime.Now).TotalDays,
                                            LastPaymentAmount = GetLastPaymentAmount(ms.MembershipID),
                                            PaymentStatus = "ACTIVE"
                                        }).ToList();

                // Risk seviyelerini hesapla
                foreach (var membership in activeMemberships)
                {
                    CalculateDeletionRisk(membership);
                }

                var result = new MemberActiveMembershipsDto
                {
                    MemberID = memberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    ActiveMemberships = activeMemberships
                };

                return new SuccessDataResult<MemberActiveMembershipsDto>(result, Messages.MemberActiveMembershipsListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberActiveMembershipsDto>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        #endregion

        #region Akıllı Üyelik Yenileme İşlemleri

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MembershipRenewalAnalysisDto> AnalyzeMembershipRenewal(int memberID, int membershipTypeID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var member = _memberDal.Get(m => m.MemberID == memberID && m.CompanyID == companyId);
                if (member == null)
                {
                    return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.MemberNotFound);
                }

                var requestedMembershipType = _membershipTypeDal.Get(mt => 
                    mt.MembershipTypeID == membershipTypeID && mt.CompanyID == companyId);
                if (requestedMembershipType == null)
                {
                    return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.MembershipTypeNotFound);
                }

                // Mevcut aktif üyelikleri getir
                var existingMemberships = GetExistingMemberships(memberID, companyId);
                
                // Analiz yap ve önerileri oluştur
                var analysis = CreateRenewalAnalysis(member, requestedMembershipType, existingMemberships);

                return new SuccessDataResult<MembershipRenewalAnalysisDto>(analysis, Messages.RenewalAnalysisCompleted);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(5)]
        [SmartCacheRemoveAspect("Member")]
        public IResult ProcessSmartMembershipRenewal(SmartMembershipRenewalDto renewalDto)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    var result = ExecuteRenewalStrategy(renewalDto);
                    
                    if (result.Success)
                    {
                        scope.Complete();
                        ClearMemberCache(renewalDto.MemberID);
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        public IDataResult<string> DetermineRenewalStrategy(int memberID, int membershipTypeID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var requestedType = _membershipTypeDal.Get(mt => 
                    mt.MembershipTypeID == membershipTypeID && mt.CompanyID == companyId);
                
                var existingMembership = _membershipDal.Get(ms =>
                    ms.MemberID == memberID &&
                    ms.MembershipTypeID == membershipTypeID &&
                    ms.CompanyID == companyId &&
                    ms.IsActive == true &&
                    ms.EndDate > DateTime.Now);

                if (existingMembership != null)
                {
                    return new SuccessDataResult<string>("EXTEND", "Mevcut üyelik uzatılacak");
                }

                var sameBranchMembership = _membershipDal.GetAll(ms =>
                    ms.MemberID == memberID &&
                    ms.CompanyID == companyId &&
                    ms.IsActive == true &&
                    ms.EndDate > DateTime.Now)
                    .Join(_membershipTypeDal.GetAll(mt => mt.CompanyID == companyId),
                          ms => ms.MembershipTypeID,
                          mt => mt.MembershipTypeID,
                          (ms, mt) => new { Membership = ms, Type = mt })
                    .FirstOrDefault(x => x.Type.Branch == requestedType.Branch);

                if (sameBranchMembership != null)
                {
                    return new SuccessDataResult<string>("UPGRADE", "Aynı branşta paket değişikliği");
                }

                return new SuccessDataResult<string>("NEW", "Yeni üyelik oluşturulacak");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        #endregion

        #region Private Helper Methods

        private void EnrichMemberData(MultiBranchMemberFilterDto member)
        {
            // Risk seviyesi hesaplama
            if (member.RemainingDays <= 3)
                member.PaymentStatus = "EXPIRING_SOON";
            else if (member.RemainingDays <= 10)
                member.PaymentStatus = "WARNING";
            else
                member.PaymentStatus = "ACTIVE";

            // Son ödeme bilgisini getir
            var lastPayment = _paymentDal.GetAll(p => 
                p.MemberShipID == member.MembershipID && 
                p.IsActive == true)
                .OrderByDescending(p => p.PaymentDate)
                .FirstOrDefault();

            if (lastPayment != null)
            {
                member.LastPaymentAmount = lastPayment.PaymentAmount;
                member.PaymentStatus = lastPayment.PaymentStatus ?? "UNKNOWN";
            }
        }

        private void CalculateDeletionRisk(ActiveMembershipDetailDto membership)
        {
            // Risk seviyesi hesaplama algoritması
            var daysSinceStart = (DateTime.Now - membership.StartDate).TotalDays;
            var totalDays = (membership.EndDate - membership.StartDate).TotalDays;
            var completionPercentage = daysSinceStart / totalDays * 100;

            if (completionPercentage < 25 && membership.LastPaymentAmount > 1000)
            {
                membership.DeletionRiskLevel = "HIGH";
                membership.DeletionWarning = "Yeni alınmış pahalı üyelik! Silme işlemi büyük finansal kayıp yaratabilir.";
            }
            else if (completionPercentage < 50)
            {
                membership.DeletionRiskLevel = "MEDIUM";
                membership.DeletionWarning = "Üyelik henüz yarısını tamamlamamış. Dikkatli olun.";
            }
            else
            {
                membership.DeletionRiskLevel = "LOW";
                membership.DeletionWarning = "Güvenli silme. Üyelik büyük oranda kullanılmış.";
            }

            // Risk tutarı hesaplama
            // Bu kod bloğu silinecek - risk tutarı AnalyzeSingleMembershipDeletion metodunda hesaplanıyor
        }

        private List<ExistingMembershipDto> GetExistingMemberships(int memberID, int companyId)
        {
            return _membershipDal.GetAll(ms =>
                ms.MemberID == memberID &&
                ms.CompanyID == companyId &&
                ms.IsActive == true &&
                ms.EndDate > DateTime.Now)
                .Join(_membershipTypeDal.GetAll(mt => mt.CompanyID == companyId),
                      ms => ms.MembershipTypeID,
                      mt => mt.MembershipTypeID,
                      (ms, mt) => new ExistingMembershipDto
                      {
                          MembershipID = ms.MembershipID,
                          MembershipTypeID = ms.MembershipTypeID,
                          Branch = mt.Branch,
                          TypeName = mt.TypeName,
                          StartDate = ms.StartDate,
                          EndDate = ms.EndDate,
                          RemainingDays = (int)(ms.EndDate - DateTime.Now).TotalDays,
                          LastPaymentAmount = GetLastPaymentAmount(ms.MembershipID)
                      }).ToList();
        }

        private decimal GetLastPaymentAmount(int membershipID)
        {
            var lastPayment = _paymentDal.GetAll(p => 
                p.MemberShipID == membershipID && 
                p.IsActive == true)
                .OrderByDescending(p => p.PaymentDate)
                .FirstOrDefault();

            return lastPayment?.PaymentAmount ?? 0;
        }

        private MembershipRenewalAnalysisDto CreateRenewalAnalysis(
            Member member, 
            MembershipType requestedType, 
            List<ExistingMembershipDto> existingMemberships)
        {
            var analysis = new MembershipRenewalAnalysisDto
            {
                MemberID = member.MemberID,
                MemberName = member.Name,
                RequestedMembershipTypeID = requestedType.MembershipTypeID,
                RequestedBranch = requestedType.Branch,
                RequestedTypeName = requestedType.TypeName,
                ExistingMemberships = existingMemberships
            };

            // Önerilen işlemi belirle
            var strategy = DetermineRenewalStrategy(member.MemberID, requestedType.MembershipTypeID);
            
            analysis.RecommendedAction = new RecommendedActionDto
            {
                ActionType = strategy.Data,
                ActionDescription = GetActionDescription(strategy.Data, requestedType),
                Reason = GetActionReason(strategy.Data, existingMemberships, requestedType),
                IsOptimal = true,
                ResultPreview = CreateResultPreview(strategy.Data, existingMemberships, requestedType)
            };

            // Alternatif seçenekleri oluştur
            analysis.AlternativeActions = CreateAlternativeActions(existingMemberships, requestedType);

            return analysis;
        }

        private string GetActionDescription(string actionType, MembershipType requestedType)
        {
            return actionType switch
            {
                "EXTEND" => $"{requestedType.Branch} - {requestedType.TypeName} üyeliğiniz {requestedType.Day} gün uzatılacak",
                "UPGRADE" => $"{requestedType.Branch} branşında {requestedType.TypeName} paketine geçiş yapılacak",
                "NEW" => $"Yeni {requestedType.Branch} - {requestedType.TypeName} üyeliği oluşturulacak",
                _ => "Bilinmeyen işlem"
            };
        }

        private string GetActionReason(string actionType, List<ExistingMembershipDto> existing, MembershipType requested)
        {
            return actionType switch
            {
                "EXTEND" => "Aynı branş ve paket türünde aktif üyeliğiniz bulunuyor",
                "UPGRADE" => "Aynı branşta farklı paket türü seçtiniz",
                "NEW" => "Farklı branş veya ilk üyelik",
                _ => "Sistem analizi"
            };
        }

        private ActionResultPreviewDto CreateResultPreview(
            string actionType, 
            List<ExistingMembershipDto> existing, 
            MembershipType requested)
        {
            var preview = new ActionResultPreviewDto
            {
                TotalActiveMemberships = actionType == "EXTEND" ? existing.Count : existing.Count + 1,
                ActiveBranches = existing.Select(e => e.Branch).Distinct().ToList()
            };

            if (actionType == "NEW" && !preview.ActiveBranches.Contains(requested.Branch))
            {
                preview.ActiveBranches.Add(requested.Branch);
            }

            preview.TotalRemainingDays = existing.Sum(e => e.RemainingDays) + 
                (actionType == "EXTEND" ? requested.Day : (actionType == "NEW" ? requested.Day : 0));

            preview.EstimatedMonthlyCost = CalculateEstimatedMonthlyCost(preview.ActiveBranches);

            return preview;
        }

        private List<AlternativeActionDto> CreateAlternativeActions(
            List<ExistingMembershipDto> existing, 
            MembershipType requested)
        {
            var alternatives = new List<AlternativeActionDto>();

            // Her zaman "Yeni üyelik oluştur" seçeneği sun
            alternatives.Add(new AlternativeActionDto
            {
                ActionType = "NEW",
                ActionDescription = "Mevcut üyelikleri koruyarak yeni üyelik oluştur",
                Pros = "Tüm branşlarda aktif kalırsınız",
                Cons = "Daha yüksek maliyet",
                ResultPreview = CreateResultPreview("NEW", existing, requested)
            });

            return alternatives;
        }

        private decimal CalculateEstimatedMonthlyCost(List<string> activeBranches)
        {
            // Basit maliyet hesaplama - gerçek implementasyonda daha karmaşık olabilir
            return activeBranches.Count * 500; // Ortalama aylık maliyet
        }

        private DeletableMembershipDto AnalyzeSingleMembershipDeletion(ActiveMembershipDetailDto membership)
        {
            var deletable = new DeletableMembershipDto
            {
                MembershipID = membership.MembershipID,
                Branch = membership.Branch,
                TypeName = membership.TypeName,
                StartDate = membership.StartDate,
                EndDate = membership.EndDate,
                RemainingDays = membership.RemainingDays,
                LastPaymentAmount = membership.LastPaymentAmount ?? 0,
                LastPaymentDate = DateTime.Now, // Bu bilgi payment tablosundan alınmalı
                PaymentStatus = membership.PaymentStatus,
                CanBeDeleted = true,
                RecommendedAction = "DELETE"
            };

            // Risk seviyesi hesaplama
            var daysSinceStart = (DateTime.Now - membership.StartDate).TotalDays;
            var totalDays = (membership.EndDate - membership.StartDate).TotalDays;
            var completionPercentage = daysSinceStart / totalDays * 100;

            if (completionPercentage < 25 && deletable.LastPaymentAmount > 1000)
            {
                deletable.RiskLevel = "HIGH";
                deletable.RiskDescription = "Yeni alınmış pahalı üyelik! Silme işlemi büyük finansal kayıp yaratabilir.";
                deletable.RiskAmount = deletable.LastPaymentAmount * 0.8m; // %80 risk
            }
            else if (completionPercentage < 50)
            {
                deletable.RiskLevel = "MEDIUM";
                deletable.RiskDescription = "Üyelik henüz yarısını tamamlamamış. Dikkatli olun.";
                deletable.RiskAmount = deletable.LastPaymentAmount * 0.5m; // %50 risk
            }
            else
            {
                deletable.RiskLevel = "LOW";
                deletable.RiskDescription = "Güvenli silme. Üyelik büyük oranda kullanılmış.";
                deletable.RiskAmount = deletable.LastPaymentAmount * 0.2m; // %20 risk
            }

            deletable.RecommendedActionDescription = GetRecommendedActionDescription(deletable.RiskLevel);

            return deletable;
        }

        private string GetRecommendedActionDescription(string riskLevel)
        {
            return riskLevel switch
            {
                "HIGH" => "Silme yerine dondurma veya transfer önerilir",
                "MEDIUM" => "Dikkatli silme - müşteri ile görüşün",
                "LOW" => "Güvenli silme",
                _ => "Analiz gerekli"
            };
        }

        private RecommendedDeletionStrategyDto DetermineRecommendedDeletionStrategy(List<DeletableMembershipDto> memberships)
        {
            var highRiskCount = memberships.Count(m => m.RiskLevel == "HIGH");
            var totalRisk = memberships.Sum(m => m.RiskAmount);

            var strategy = new RecommendedDeletionStrategyDto
            {
                MembershipIDsToDelete = new List<int>(),
                MembershipIDsToKeep = new List<int>(),
                EstimatedFinancialImpact = totalRisk
            };

            if (highRiskCount > memberships.Count / 2)
            {
                // Çok riskli - seçici silme öner
                strategy.StrategyType = "SELECTIVE";
                strategy.StrategyDescription = "Seçici silme önerilir - yüksek riskli üyelikleri koruyun";
                strategy.Reasoning = "Çok sayıda yüksek riskli üyelik mevcut";
                strategy.CustomerSatisfactionRisk = 8;

                // Sadece düşük riskli üyelikleri sil
                strategy.MembershipIDsToDelete = memberships
                    .Where(m => m.RiskLevel == "LOW")
                    .Select(m => m.MembershipID)
                    .ToList();

                strategy.MembershipIDsToKeep = memberships
                    .Where(m => m.RiskLevel != "LOW")
                    .Select(m => m.MembershipID)
                    .ToList();
            }
            else if (totalRisk < 500) // Düşük toplam risk
            {
                // Tam silme öner
                strategy.StrategyType = "COMPLETE";
                strategy.StrategyDescription = "Tüm üyeliklerin silinmesi güvenli";
                strategy.Reasoning = "Toplam finansal risk düşük";
                strategy.CustomerSatisfactionRisk = 3;

                strategy.MembershipIDsToDelete = memberships.Select(m => m.MembershipID).ToList();
            }
            else
            {
                // Orta risk - dondurma öner
                strategy.StrategyType = "FREEZE";
                strategy.StrategyDescription = "Silme yerine dondurma önerilir";
                strategy.Reasoning = "Orta seviye risk - dondurma daha güvenli";
                strategy.CustomerSatisfactionRisk = 5;

                // Hiçbirini silme
                strategy.MembershipIDsToKeep = memberships.Select(m => m.MembershipID).ToList();
            }

            return strategy;
        }

        private List<string> GenerateDeletionWarnings(List<DeletableMembershipDto> memberships)
        {
            var warnings = new List<string>();

            var highRiskCount = memberships.Count(m => m.RiskLevel == "HIGH");
            if (highRiskCount > 0)
            {
                warnings.Add($"{highRiskCount} adet yüksek riskli üyelik mevcut");
            }

            var totalRisk = memberships.Sum(m => m.RiskAmount);
            if (totalRisk > 2000)
            {
                warnings.Add($"Toplam finansal risk yüksek: {totalRisk:C}");
            }

            var recentMemberships = memberships.Count(m =>
                (DateTime.Now - m.StartDate).TotalDays < 30);
            if (recentMemberships > 0)
            {
                warnings.Add($"{recentMemberships} adet son 30 günde alınmış üyelik var");
            }

            return warnings;
        }

        private IResult DeleteSingleMembershipInternal(int membershipID, string? deletionReason)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();

                var membership = _membershipDal.Get(ms =>
                    ms.MembershipID == membershipID &&
                    ms.CompanyID == companyId);

                if (membership == null)
                {
                    return new ErrorResult("Üyelik bulunamadı");
                }

                // Soft delete
                membership.IsActive = false;
                membership.UpdatedDate = DateTime.Now;

                _membershipDal.Update(membership);

                // Silme kaydı oluştur (audit için)
                // Bu kısım audit sistemi implementasyonunda detaylandırılabilir

                return new SuccessResult("Üyelik başarıyla silindi");
            }
            catch (Exception ex)
            {
                return new ErrorResult("Üyelik silinirken hata oluştu: " + ex.Message);
            }
        }

        private IResult ExecuteRenewalStrategy(SmartMembershipRenewalDto renewalDto)
        {
            switch (renewalDto.RenewalStrategy)
            {
                case "EXTEND":
                    return ExtendExistingMembership(renewalDto);
                case "NEW":
                    return CreateNewMembership(renewalDto);
                case "UPGRADE":
                    return UpgradeMembership(renewalDto);
                default:
                    return new ErrorResult("Geçersiz yenileme stratejisi");
            }
        }

        private IResult ExtendExistingMembership(SmartMembershipRenewalDto renewalDto)
        {
            if (!renewalDto.ExistingMembershipID.HasValue)
            {
                return new ErrorResult("Uzatılacak üyelik ID'si belirtilmemiş");
            }

            var existingMembership = _membershipDal.Get(ms => 
                ms.MembershipID == renewalDto.ExistingMembershipID.Value);

            if (existingMembership == null)
            {
                return new ErrorResult("Uzatılacak üyelik bulunamadı");
            }

            existingMembership.EndDate = existingMembership.EndDate.AddDays(renewalDto.Day);
            existingMembership.UpdatedDate = DateTime.Now;

            _membershipDal.Update(existingMembership);

            // Ödeme kaydı oluştur
            CreatePaymentRecord(existingMembership.MembershipID, renewalDto);

            return new SuccessResult("Üyelik başarıyla uzatıldı");
        }

        private IResult CreateNewMembership(SmartMembershipRenewalDto renewalDto)
        {
            var newMembership = new Membership
            {
                MemberID = renewalDto.MemberID,
                MembershipTypeID = renewalDto.MembershipTypeID,
                StartDate = renewalDto.StartDate,
                EndDate = renewalDto.EndDate,
                CompanyID = _companyContext.GetCompanyId(),
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _membershipDal.Add(newMembership);

            // Ödeme kaydı oluştur
            CreatePaymentRecord(newMembership.MembershipID, renewalDto);

            return new SuccessResult("Yeni üyelik başarıyla oluşturuldu");
        }

        private IResult UpgradeMembership(SmartMembershipRenewalDto renewalDto)
        {
            // Upgrade işlemi - yeni üyelik oluştur, eski üyeliği pasif yap veya koru
            return CreateNewMembership(renewalDto);
        }

        private void CreatePaymentRecord(int membershipID, SmartMembershipRenewalDto renewalDto)
        {
            var payment = new Payment
            {
                MemberShipID = membershipID,
                CompanyID = _companyContext.GetCompanyId(),
                PaymentAmount = renewalDto.Price,
                PaymentMethod = renewalDto.PaymentMethod,
                OriginalPaymentMethod = renewalDto.PaymentMethod,
                FinalPaymentMethod = renewalDto.PaymentMethod,
                PaymentDate = DateTime.Now,
                PaymentStatus = renewalDto.PaymentMethod == "Borç" ? "Pending" : "Completed",
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _paymentDal.Add(payment);
        }

        #endregion

        #region Cache ve Performans İşlemleri

        [SmartCacheRemoveAspect("Member")]
        public IResult ClearCache()
        {
            return new SuccessResult("Cache temizlendi");
        }

        [SmartCacheRemoveAspect("Member")]
        public IResult ClearMemberCache(int memberID)
        {
            return new SuccessResult($"Üye {memberID} cache'i temizlendi");
        }

        public IDataResult<Dictionary<string, object>> GetPerformanceMetrics()
        {
            var metrics = new Dictionary<string, object>
            {
                {"TotalActiveMembers", _memberDal.GetAll(m => m.IsActive == true).Count},
                {"TotalActiveMemberships", _membershipDal.GetAll(ms => ms.IsActive == true && ms.EndDate > DateTime.Now).Count},
                {"CacheHitRate", "95%"}, // Gerçek implementasyonda cache servisinden alınır
                {"AvgResponseTime", "150ms"}
            };

            return new SuccessDataResult<Dictionary<string, object>>(metrics);
        }

        #endregion

        #region Diğer Interface Metodları (Placeholder)

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MembershipDeletionAnalysisDto> AnalyzeMembershipDeletion(int memberID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();

                var member = _memberDal.Get(m => m.MemberID == memberID && m.CompanyID == companyId);
                if (member == null)
                {
                    return new ErrorDataResult<MembershipDeletionAnalysisDto>(Messages.MemberNotFound);
                }

                // Üyenin aktif üyeliklerini getir
                var activeMemberships = GetMemberActiveMemberships(memberID);
                if (!activeMemberships.Success || activeMemberships.Data == null)
                {
                    return new ErrorDataResult<MembershipDeletionAnalysisDto>("Aktif üyelik bulunamadı");
                }

                var analysis = new MembershipDeletionAnalysisDto
                {
                    MemberID = memberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    DeletableMemberships = new List<DeletableMembershipDto>(),
                    Warnings = new List<string>()
                };

                // Her üyelik için silme analizi yap
                foreach (var membership in activeMemberships.Data.ActiveMemberships)
                {
                    var deletableMembership = AnalyzeSingleMembershipDeletion(membership);
                    analysis.DeletableMemberships.Add(deletableMembership);
                }

                // Toplam değerleri hesapla - readonly property'ler otomatik hesaplanıyor
                // analysis.TotalActiveMemberships ve analysis.TotalRiskAmount readonly property'ler

                // Önerilen stratejiyi belirle
                analysis.RecommendedStrategy = DetermineRecommendedDeletionStrategy(analysis.DeletableMemberships);

                // Uyarıları oluştur
                analysis.Warnings = GenerateDeletionWarnings(analysis.DeletableMemberships);

                return new SuccessDataResult<MembershipDeletionAnalysisDto>(analysis, "Silme analizi tamamlandı");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MembershipDeletionAnalysisDto>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(5)]
        [SmartCacheRemoveAspect("Member")]
        public IResult ProcessSmartMembershipDeletion(SmartMembershipDeletionRequestDto deletionRequest)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    int companyId = _companyContext.GetCompanyId();

                    // Validasyonlar
                    if (deletionRequest.MemberID <= 0)
                    {
                        return new ErrorResult("Geçersiz üye ID'si");
                    }

                    if (!deletionRequest.UserConfirmed)
                    {
                        return new ErrorResult("Kullanıcı onayı gerekli");
                    }

                    if (deletionRequest.MembershipIDsToDelete == null || deletionRequest.MembershipIDsToDelete.Count == 0)
                    {
                        return new ErrorResult("Silinecek üyelik seçilmemiş");
                    }

                    var result = new MembershipDeletionResultDto
                    {
                        DeletedMembershipIDs = new List<int>(),
                        FailedDeletions = new Dictionary<int, string>()
                    };

                    // Her üyeliği sil
                    foreach (var membershipID in deletionRequest.MembershipIDsToDelete)
                    {
                        var deleteResult = DeleteSingleMembershipInternal(membershipID, deletionRequest.DeletionReason);

                        if (deleteResult.Success)
                        {
                            result.DeletedMembershipIDs.Add(membershipID);
                        }
                        else
                        {
                            result.FailedDeletions[membershipID] = deleteResult.Message;
                        }
                    }

                    // Sonuç değerlendirmesi
                    if (result.DeletedMembershipIDs.Count > 0)
                    {
                        scope.Complete();
                        ClearMemberCache(deletionRequest.MemberID);

                        var message = $"{result.DeletedMembershipIDs.Count} üyelik başarıyla silindi";
                        if (result.FailedDeletions.Count > 0)
                        {
                            message += $", {result.FailedDeletions.Count} üyelik silinemedi";
                        }

                        return new SuccessResult(message);
                    }
                    else
                    {
                        return new ErrorResult("Hiçbir üyelik silinemedi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("Member")]
        public IResult DeleteSingleMembership(int membershipID, string? deletionReason = null)
        {
            try
            {
                var result = DeleteSingleMembershipInternal(membershipID, deletionReason);

                if (result.Success)
                {
                    // Cache temizle
                    var membership = _membershipDal.Get(ms => ms.MembershipID == membershipID);
                    if (membership != null)
                    {
                        ClearMemberCache(membership.MemberID);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                return new ErrorResult(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("Member")]
        public IResult DeleteAllMemberships(int memberID, string? deletionReason = null)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    int companyId = _companyContext.GetCompanyId();

                    var activeMemberships = _membershipDal.GetAll(ms =>
                        ms.MemberID == memberID &&
                        ms.CompanyID == companyId &&
                        ms.IsActive == true &&
                        ms.EndDate > DateTime.Now);

                    if (!activeMemberships.Any())
                    {
                        return new ErrorResult("Silinecek aktif üyelik bulunamadı");
                    }

                    int deletedCount = 0;
                    foreach (var membership in activeMemberships)
                    {
                        var deleteResult = DeleteSingleMembershipInternal(membership.MembershipID, deletionReason);
                        if (deleteResult.Success)
                        {
                            deletedCount++;
                        }
                    }

                    if (deletedCount > 0)
                    {
                        scope.Complete();
                        ClearMemberCache(memberID);
                        return new SuccessResult($"{deletedCount} üyelik başarıyla silindi");
                    }
                    else
                    {
                        return new ErrorResult("Hiçbir üyelik silinemedi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        public IDataResult<MemberCurrentStatusDto> GetMemberCurrentStatus(int memberID)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<Dictionary<string, object>> GetBranchStatistics()
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<bool> CheckMembershipConflict(int memberID, int membershipTypeID)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<List<object>> GetMembershipHistory(int memberID, int? branchFilter = null)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        #endregion
    }
}
