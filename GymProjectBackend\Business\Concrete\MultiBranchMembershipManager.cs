using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Secured;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    /// <summary>
    /// Çoklu branş üyelik yönetimi manager sınıfı
    /// Akıllı yenileme, gü<PERSON>li silme ve iki seviyeli filtreleme işlemlerini yönetir
    /// </summary>
    public class MultiBranchMembershipManager : IMultiBranchMembershipService
    {
        private readonly IMembershipDal _membershipDal;
        private readonly IMemberDal _memberDal;
        private readonly IMembershipTypeDal _membershipTypeDal;
        private readonly IPaymentDal _paymentDal;
        private readonly ICompanyContext _companyContext;

        public MultiBranchMembershipManager(
            IMembershipDal membershipDal,
            IMemberDal memberDal,
            IMembershipTypeDal membershipTypeDal,
            IPaymentDal paymentDal,
            ICompanyContext companyContext)
        {
            _membershipDal = membershipDal;
            _memberDal = memberDal;
            _membershipTypeDal = membershipTypeDal;
            _paymentDal = paymentDal;
            _companyContext = companyContext;
        }

        #region Çoklu Branş Filtreleme İşlemleri

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(duration: 5)] // 5 dakika cache
        public IDataResult<List<MultiBranchMemberFilterDto>> GetMultiBranchMemberDetails(
            string searchText = null, 
            int? gender = null, 
            string branchFilter = null, 
            int pageNumber = 1, 
            int pageSize = 50)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Geçici olarak normal sorgu kullan, stored procedure daha sonra eklenecek
                var query = from m in _memberDal.GetAll(mem => mem.CompanyID == companyId && mem.IsActive == true)
                           join ms in _membershipDal.GetAll(membership =>
                               membership.CompanyID == companyId &&
                               membership.IsActive == true &&
                               membership.EndDate > DateTime.Now &&
                               membership.IsFrozen == false)
                           on m.MemberID equals ms.MemberID
                           join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId && type.IsActive == true)
                           on ms.MembershipTypeID equals mt.MembershipTypeID
                           where (string.IsNullOrEmpty(searchText) || m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText))
                              && (!gender.HasValue || m.Gender == gender.Value)
                              && (string.IsNullOrEmpty(branchFilter) || mt.Branch == branchFilter)
                           select new MultiBranchMemberFilterDto
                           {
                               MemberID = m.MemberID,
                               MembershipID = ms.MembershipID,
                               MembershipTypeID = ms.MembershipTypeID,
                               Name = m.Name,
                               Gender = m.Gender,
                               PhoneNumber = m.PhoneNumber,
                               Branch = mt.Branch,
                               TypeName = mt.TypeName,
                               StartDate = ms.StartDate,
                               EndDate = ms.EndDate,
                               RemainingDays = ms.StartDate > DateTime.Now ?
                                   (int)(ms.EndDate - ms.StartDate).TotalDays :
                                   (int)(ms.EndDate - DateTime.Now).TotalDays,
                               IsActive = ms.IsActive ?? true,
                               IsFutureStartDate = ms.StartDate > DateTime.Now,
                               UpdatedDate = ms.UpdatedDate ?? ms.CreationDate,
                               Balance = m.Balance,
                               IsFrozen = ms.IsFrozen
                           };

                var result = query.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();

                // Her üye için ek bilgileri zenginleştir
                foreach (var member in result)
                {
                    EnrichMemberData(member);
                }

                return new SuccessDataResult<List<MultiBranchMemberFilterDto>>(result, Messages.MembersListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<MultiBranchMemberFilterDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        [CacheAspect(duration: 10)] // 10 dakika cache
        public IDataResult<List<BranchSummaryDto>> GetBranchSummary()
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Geçici olarak normal sorgu kullan
                var branchData = from ms in _membershipDal.GetAll(membership =>
                                    membership.CompanyID == companyId &&
                                    membership.IsActive == true &&
                                    membership.EndDate > DateTime.Now)
                                join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId)
                                on ms.MembershipTypeID equals mt.MembershipTypeID
                                join m in _memberDal.GetAll(member => member.CompanyID == companyId && member.IsActive == true)
                                on ms.MemberID equals m.MemberID
                                group new { ms, mt, m } by mt.Branch into g
                                select new BranchSummaryDto
                                {
                                    Branch = g.Key,
                                    MemberCount = g.Select(x => x.m.MemberID).Distinct().Count(),
                                    TotalRemainingDays = g.Sum(x => x.ms.EndDate > DateTime.Now ?
                                        (int)(x.ms.EndDate - DateTime.Now).TotalDays : 0),
                                    AvgRemainingDays = (int)g.Average(x => x.ms.EndDate > DateTime.Now ?
                                        (x.ms.EndDate - DateTime.Now).TotalDays : 0)
                                };

                var result = branchData.ToList();

                // Her branş için paket türlerini ekle
                foreach (var branch in result)
                {
                    branch.PackageTypes = GetPackageTypesByBranch(branch.Branch).Data ?? new List<PackageTypeCountDto>();
                }

                return new SuccessDataResult<List<BranchSummaryDto>>(result, Messages.BranchSummaryListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<BranchSummaryDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        public IDataResult<List<PackageTypeCountDto>> GetPackageTypesByBranch(string branch)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                // Belirli branştaki paket türlerini ve üye sayılarını getir
                var packageTypes = _membershipTypeDal.GetAll(mt => 
                    mt.CompanyID == companyId && 
                    mt.Branch == branch && 
                    mt.IsActive == true);

                var result = new List<PackageTypeCountDto>();

                foreach (var packageType in packageTypes)
                {
                    var memberCount = _membershipDal.GetAll(ms => 
                        ms.CompanyID == companyId &&
                        ms.MembershipTypeID == packageType.MembershipTypeID &&
                        ms.IsActive == true &&
                        ms.EndDate > DateTime.Now).Count;

                    var totalRemainingDays = _membershipDal.GetAll(ms => 
                        ms.CompanyID == companyId &&
                        ms.MembershipTypeID == packageType.MembershipTypeID &&
                        ms.IsActive == true &&
                        ms.EndDate > DateTime.Now)
                        .Sum(ms => (int)(ms.EndDate - DateTime.Now).TotalDays);

                    result.Add(new PackageTypeCountDto
                    {
                        MembershipTypeID = packageType.MembershipTypeID,
                        TypeName = packageType.TypeName,
                        Day = packageType.Day,
                        Price = packageType.Price,
                        MemberCount = memberCount,
                        TotalRemainingDays = totalRemainingDays
                    });
                }

                return new SuccessDataResult<List<PackageTypeCountDto>>(result, Messages.PackageTypesListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<PackageTypeCountDto>>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(2)]
        public IDataResult<MemberActiveMembershipsDto> GetMemberActiveMemberships(int memberID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var member = _memberDal.Get(m => m.MemberID == memberID && m.CompanyID == companyId);
                if (member == null)
                {
                    return new ErrorDataResult<MemberActiveMembershipsDto>(Messages.MemberNotFound);
                }

                // Geçici olarak normal sorgu kullan
                var activeMemberships = (from ms in _membershipDal.GetAll(membership =>
                                            membership.MemberID == memberID &&
                                            membership.CompanyID == companyId &&
                                            membership.IsActive == true &&
                                            membership.EndDate > DateTime.Now)
                                        join mt in _membershipTypeDal.GetAll(type => type.CompanyID == companyId)
                                        on ms.MembershipTypeID equals mt.MembershipTypeID
                                        select new ActiveMembershipDetailDto
                                        {
                                            MembershipID = ms.MembershipID,
                                            Branch = mt.Branch,
                                            TypeName = mt.TypeName,
                                            StartDate = ms.StartDate,
                                            EndDate = ms.EndDate,
                                            RemainingDays = (int)(ms.EndDate - DateTime.Now).TotalDays,
                                            LastPaymentAmount = GetLastPaymentAmount(ms.MembershipID),
                                            PaymentStatus = "ACTIVE"
                                        }).ToList();

                // Risk seviyelerini hesapla
                foreach (var membership in activeMemberships)
                {
                    CalculateDeletionRisk(membership);
                }

                var result = new MemberActiveMembershipsDto
                {
                    MemberID = memberID,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    ActiveMemberships = activeMemberships
                };

                return new SuccessDataResult<MemberActiveMembershipsDto>(result, Messages.MemberActiveMembershipsListed);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberActiveMembershipsDto>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        #endregion

        #region Akıllı Üyelik Yenileme İşlemleri

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MembershipRenewalAnalysisDto> AnalyzeMembershipRenewal(int memberID, int membershipTypeID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var member = _memberDal.Get(m => m.MemberID == memberID && m.CompanyID == companyId);
                if (member == null)
                {
                    return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.MemberNotFound);
                }

                var requestedMembershipType = _membershipTypeDal.Get(mt => 
                    mt.MembershipTypeID == membershipTypeID && mt.CompanyID == companyId);
                if (requestedMembershipType == null)
                {
                    return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.MembershipTypeNotFound);
                }

                // Mevcut aktif üyelikleri getir
                var existingMemberships = GetExistingMemberships(memberID, companyId);
                
                // Analiz yap ve önerileri oluştur
                var analysis = CreateRenewalAnalysis(member, requestedMembershipType, existingMemberships);

                return new SuccessDataResult<MembershipRenewalAnalysisDto>(analysis, Messages.RenewalAnalysisCompleted);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MembershipRenewalAnalysisDto>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(5)]
        [SmartCacheRemoveAspect("Member")]
        public IResult ProcessSmartMembershipRenewal(SmartMembershipRenewalDto renewalDto)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    var result = ExecuteRenewalStrategy(renewalDto);
                    
                    if (result.Success)
                    {
                        scope.Complete();
                        ClearMemberCache(renewalDto.MemberID);
                    }
                    
                    return result;
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        public IDataResult<string> DetermineRenewalStrategy(int memberID, int membershipTypeID)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();
                
                var requestedType = _membershipTypeDal.Get(mt => 
                    mt.MembershipTypeID == membershipTypeID && mt.CompanyID == companyId);
                
                var existingMembership = _membershipDal.Get(ms =>
                    ms.MemberID == memberID &&
                    ms.MembershipTypeID == membershipTypeID &&
                    ms.CompanyID == companyId &&
                    ms.IsActive == true &&
                    ms.EndDate > DateTime.Now);

                if (existingMembership != null)
                {
                    return new SuccessDataResult<string>("EXTEND", "Mevcut üyelik uzatılacak");
                }

                var sameBranchMembership = _membershipDal.GetAll(ms =>
                    ms.MemberID == memberID &&
                    ms.CompanyID == companyId &&
                    ms.IsActive == true &&
                    ms.EndDate > DateTime.Now)
                    .Join(_membershipTypeDal.GetAll(mt => mt.CompanyID == companyId),
                          ms => ms.MembershipTypeID,
                          mt => mt.MembershipTypeID,
                          (ms, mt) => new { Membership = ms, Type = mt })
                    .FirstOrDefault(x => x.Type.Branch == requestedType.Branch);

                if (sameBranchMembership != null)
                {
                    return new SuccessDataResult<string>("UPGRADE", "Aynı branşta paket değişikliği");
                }

                return new SuccessDataResult<string>("NEW", "Yeni üyelik oluşturulacak");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string>(Messages.ErrorOccurred + ": " + ex.Message);
            }
        }

        #endregion

        #region Private Helper Methods

        private void EnrichMemberData(MultiBranchMemberFilterDto member)
        {
            // Risk seviyesi hesaplama
            if (member.RemainingDays <= 3)
                member.PaymentStatus = "EXPIRING_SOON";
            else if (member.RemainingDays <= 10)
                member.PaymentStatus = "WARNING";
            else
                member.PaymentStatus = "ACTIVE";

            // Son ödeme bilgisini getir
            var lastPayment = _paymentDal.GetAll(p => 
                p.MemberShipID == member.MembershipID && 
                p.IsActive == true)
                .OrderByDescending(p => p.PaymentDate)
                .FirstOrDefault();

            if (lastPayment != null)
            {
                member.LastPaymentAmount = lastPayment.PaymentAmount;
                member.PaymentStatus = lastPayment.PaymentStatus ?? "UNKNOWN";
            }
        }

        private void CalculateDeletionRisk(ActiveMembershipDetailDto membership)
        {
            // Risk seviyesi hesaplama algoritması
            var daysSinceStart = (DateTime.Now - membership.StartDate).TotalDays;
            var totalDays = (membership.EndDate - membership.StartDate).TotalDays;
            var completionPercentage = daysSinceStart / totalDays * 100;

            if (completionPercentage < 25 && membership.LastPaymentAmount > 1000)
            {
                membership.DeletionRiskLevel = "HIGH";
                membership.DeletionWarning = "Yeni alınmış pahalı üyelik! Silme işlemi büyük finansal kayıp yaratabilir.";
            }
            else if (completionPercentage < 50)
            {
                membership.DeletionRiskLevel = "MEDIUM";
                membership.DeletionWarning = "Üyelik henüz yarısını tamamlamamış. Dikkatli olun.";
            }
            else
            {
                membership.DeletionRiskLevel = "LOW";
                membership.DeletionWarning = "Güvenli silme. Üyelik büyük oranda kullanılmış.";
            }

            // Risk tutarı hesaplama
            membership.RiskAmount = (decimal)(membership.LastPaymentAmount * (membership.RemainingDays / totalDays));
        }

        private List<ExistingMembershipDto> GetExistingMemberships(int memberID, int companyId)
        {
            return _membershipDal.GetAll(ms =>
                ms.MemberID == memberID &&
                ms.CompanyID == companyId &&
                ms.IsActive == true &&
                ms.EndDate > DateTime.Now)
                .Join(_membershipTypeDal.GetAll(mt => mt.CompanyID == companyId),
                      ms => ms.MembershipTypeID,
                      mt => mt.MembershipTypeID,
                      (ms, mt) => new ExistingMembershipDto
                      {
                          MembershipID = ms.MembershipID,
                          MembershipTypeID = ms.MembershipTypeID,
                          Branch = mt.Branch,
                          TypeName = mt.TypeName,
                          StartDate = ms.StartDate,
                          EndDate = ms.EndDate,
                          RemainingDays = (int)(ms.EndDate - DateTime.Now).TotalDays,
                          LastPaymentAmount = GetLastPaymentAmount(ms.MembershipID)
                      }).ToList();
        }

        private decimal GetLastPaymentAmount(int membershipID)
        {
            var lastPayment = _paymentDal.GetAll(p => 
                p.MemberShipID == membershipID && 
                p.IsActive == true)
                .OrderByDescending(p => p.PaymentDate)
                .FirstOrDefault();

            return lastPayment?.PaymentAmount ?? 0;
        }

        private MembershipRenewalAnalysisDto CreateRenewalAnalysis(
            Member member, 
            MembershipType requestedType, 
            List<ExistingMembershipDto> existingMemberships)
        {
            var analysis = new MembershipRenewalAnalysisDto
            {
                MemberID = member.MemberID,
                MemberName = member.Name,
                RequestedMembershipTypeID = requestedType.MembershipTypeID,
                RequestedBranch = requestedType.Branch,
                RequestedTypeName = requestedType.TypeName,
                ExistingMemberships = existingMemberships
            };

            // Önerilen işlemi belirle
            var strategy = DetermineRenewalStrategy(member.MemberID, requestedType.MembershipTypeID);
            
            analysis.RecommendedAction = new RecommendedActionDto
            {
                ActionType = strategy.Data,
                ActionDescription = GetActionDescription(strategy.Data, requestedType),
                Reason = GetActionReason(strategy.Data, existingMemberships, requestedType),
                IsOptimal = true,
                ResultPreview = CreateResultPreview(strategy.Data, existingMemberships, requestedType)
            };

            // Alternatif seçenekleri oluştur
            analysis.AlternativeActions = CreateAlternativeActions(existingMemberships, requestedType);

            return analysis;
        }

        private string GetActionDescription(string actionType, MembershipType requestedType)
        {
            return actionType switch
            {
                "EXTEND" => $"{requestedType.Branch} - {requestedType.TypeName} üyeliğiniz {requestedType.Day} gün uzatılacak",
                "UPGRADE" => $"{requestedType.Branch} branşında {requestedType.TypeName} paketine geçiş yapılacak",
                "NEW" => $"Yeni {requestedType.Branch} - {requestedType.TypeName} üyeliği oluşturulacak",
                _ => "Bilinmeyen işlem"
            };
        }

        private string GetActionReason(string actionType, List<ExistingMembershipDto> existing, MembershipType requested)
        {
            return actionType switch
            {
                "EXTEND" => "Aynı branş ve paket türünde aktif üyeliğiniz bulunuyor",
                "UPGRADE" => "Aynı branşta farklı paket türü seçtiniz",
                "NEW" => "Farklı branş veya ilk üyelik",
                _ => "Sistem analizi"
            };
        }

        private ActionResultPreviewDto CreateResultPreview(
            string actionType, 
            List<ExistingMembershipDto> existing, 
            MembershipType requested)
        {
            var preview = new ActionResultPreviewDto
            {
                TotalActiveMemberships = actionType == "EXTEND" ? existing.Count : existing.Count + 1,
                ActiveBranches = existing.Select(e => e.Branch).Distinct().ToList()
            };

            if (actionType == "NEW" && !preview.ActiveBranches.Contains(requested.Branch))
            {
                preview.ActiveBranches.Add(requested.Branch);
            }

            preview.TotalRemainingDays = existing.Sum(e => e.RemainingDays) + 
                (actionType == "EXTEND" ? requested.Day : (actionType == "NEW" ? requested.Day : 0));

            preview.EstimatedMonthlyCost = CalculateEstimatedMonthlyCost(preview.ActiveBranches);

            return preview;
        }

        private List<AlternativeActionDto> CreateAlternativeActions(
            List<ExistingMembershipDto> existing, 
            MembershipType requested)
        {
            var alternatives = new List<AlternativeActionDto>();

            // Her zaman "Yeni üyelik oluştur" seçeneği sun
            alternatives.Add(new AlternativeActionDto
            {
                ActionType = "NEW",
                ActionDescription = "Mevcut üyelikleri koruyarak yeni üyelik oluştur",
                Pros = "Tüm branşlarda aktif kalırsınız",
                Cons = "Daha yüksek maliyet",
                ResultPreview = CreateResultPreview("NEW", existing, requested)
            });

            return alternatives;
        }

        private decimal CalculateEstimatedMonthlyCost(List<string> activeBranches)
        {
            // Basit maliyet hesaplama - gerçek implementasyonda daha karmaşık olabilir
            return activeBranches.Count * 500; // Ortalama aylık maliyet
        }

        private IResult ExecuteRenewalStrategy(SmartMembershipRenewalDto renewalDto)
        {
            switch (renewalDto.RenewalStrategy)
            {
                case "EXTEND":
                    return ExtendExistingMembership(renewalDto);
                case "NEW":
                    return CreateNewMembership(renewalDto);
                case "UPGRADE":
                    return UpgradeMembership(renewalDto);
                default:
                    return new ErrorResult("Geçersiz yenileme stratejisi");
            }
        }

        private IResult ExtendExistingMembership(SmartMembershipRenewalDto renewalDto)
        {
            if (!renewalDto.ExistingMembershipID.HasValue)
            {
                return new ErrorResult("Uzatılacak üyelik ID'si belirtilmemiş");
            }

            var existingMembership = _membershipDal.Get(ms => 
                ms.MembershipID == renewalDto.ExistingMembershipID.Value);

            if (existingMembership == null)
            {
                return new ErrorResult("Uzatılacak üyelik bulunamadı");
            }

            existingMembership.EndDate = existingMembership.EndDate.AddDays(renewalDto.Day);
            existingMembership.UpdatedDate = DateTime.Now;

            _membershipDal.Update(existingMembership);

            // Ödeme kaydı oluştur
            CreatePaymentRecord(existingMembership.MembershipID, renewalDto);

            return new SuccessResult("Üyelik başarıyla uzatıldı");
        }

        private IResult CreateNewMembership(SmartMembershipRenewalDto renewalDto)
        {
            var newMembership = new Membership
            {
                MemberID = renewalDto.MemberID,
                MembershipTypeID = renewalDto.MembershipTypeID,
                StartDate = renewalDto.StartDate,
                EndDate = renewalDto.EndDate,
                CompanyID = _companyContext.GetCompanyId(),
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _membershipDal.Add(newMembership);

            // Ödeme kaydı oluştur
            CreatePaymentRecord(newMembership.MembershipID, renewalDto);

            return new SuccessResult("Yeni üyelik başarıyla oluşturuldu");
        }

        private IResult UpgradeMembership(SmartMembershipRenewalDto renewalDto)
        {
            // Upgrade işlemi - yeni üyelik oluştur, eski üyeliği pasif yap veya koru
            return CreateNewMembership(renewalDto);
        }

        private void CreatePaymentRecord(int membershipID, SmartMembershipRenewalDto renewalDto)
        {
            var payment = new Payment
            {
                MemberShipID = membershipID,
                CompanyID = _companyContext.GetCompanyId(),
                PaymentAmount = renewalDto.Price,
                PaymentMethod = renewalDto.PaymentMethod,
                OriginalPaymentMethod = renewalDto.PaymentMethod,
                FinalPaymentMethod = renewalDto.PaymentMethod,
                PaymentDate = DateTime.Now,
                PaymentStatus = renewalDto.PaymentMethod == "Borç" ? "Pending" : "Completed",
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _paymentDal.Add(payment);
        }

        #endregion

        #region Cache ve Performans İşlemleri

        [SmartCacheRemoveAspect("Member")]
        public IResult ClearCache()
        {
            return new SuccessResult("Cache temizlendi");
        }

        [SmartCacheRemoveAspect("Member")]
        public IResult ClearMemberCache(int memberID)
        {
            return new SuccessResult($"Üye {memberID} cache'i temizlendi");
        }

        public IDataResult<Dictionary<string, object>> GetPerformanceMetrics()
        {
            var metrics = new Dictionary<string, object>
            {
                {"TotalActiveMembers", _memberDal.GetAll(m => m.IsActive == true).Count},
                {"TotalActiveMemberships", _membershipDal.GetAll(ms => ms.IsActive == true && ms.EndDate > DateTime.Now).Count},
                {"CacheHitRate", "95%"}, // Gerçek implementasyonda cache servisinden alınır
                {"AvgResponseTime", "150ms"}
            };

            return new SuccessDataResult<Dictionary<string, object>>(metrics);
        }

        #endregion

        #region Diğer Interface Metodları (Placeholder)

        public IDataResult<MembershipDeletionAnalysisDto> AnalyzeMembershipDeletion(int memberID)
        {
            // Bu metod SmartMembershipDeletionDto implementasyonunda detaylandırılacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IResult ProcessSmartMembershipDeletion(SmartMembershipDeletionRequestDto deletionRequest)
        {
            // Bu metod SmartMembershipDeletionDto implementasyonunda detaylandırılacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IResult DeleteSingleMembership(int membershipID, string deletionReason = null)
        {
            // Bu metod SmartMembershipDeletionDto implementasyonunda detaylandırılacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IResult DeleteAllMemberships(int memberID, string deletionReason = null)
        {
            // Bu metod SmartMembershipDeletionDto implementasyonunda detaylandırılacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<MemberCurrentStatusDto> GetMemberCurrentStatus(int memberID)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<Dictionary<string, object>> GetBranchStatistics()
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<bool> CheckMembershipConflict(int memberID, int membershipTypeID)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        public IDataResult<List<object>> GetMembershipHistory(int memberID, int? branchFilter = null)
        {
            // Bu metod ilerleyen aşamalarda implementasyona alınacak
            throw new NotImplementedException("Bu metod henüz implementasyona alınmamıştır");
        }

        #endregion
    }
}
