-- =====================================================
-- Çok Branşlı Üyelik Sistemi - Veritabanı Güncellemeleri
-- Tarih: 2025-06-21
-- Amaç: Çoklu branş üyeliği, akıllı yenileme ve performans optimizasyonu
-- =====================================================

-- 1. PERFORMANS İNDEXLERİ
-- =====================================================

-- Üyelik filtreleme için ana index (en kritik)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Membership_Member_Type_Active_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Membership_Member_Type_Active_Performance
    ON Memberships (CompanyID, IsActive, EndDate, MemberID, MembershipTypeID)
    INCLUDE (StartDate, CreationDate, UpdatedDate, IsFrozen)
    WITH (FILLFACTOR = 90, PAD_INDEX = ON)
END
GO

-- Branş bazlı filtreleme için index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MembershipType_Company_Branch_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX IX_MembershipType_Company_Branch_Performance
    ON MembershipTypes (CompanyID, Branch, IsActive)
    INCLUDE (MembershipTypeID, TypeName, Day, Price)
    WITH (FILLFACTOR = 90, PAD_INDEX = ON)
END
GO

-- Üye bazlı sorgular için index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Member_Company_Active_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Member_Company_Active_Performance
    ON Members (CompanyID, IsActive, MemberID)
    INCLUDE (Name, PhoneNumber, Gender, Email, Balance)
    WITH (FILLFACTOR = 90, PAD_INDEX = ON)
END
GO

-- Ödeme geçmişi için index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payment_Membership_Company_Performance')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Payment_Membership_Company_Performance
    ON Payments (CompanyID, MemberShipID, IsActive, PaymentDate)
    INCLUDE (PaymentAmount, PaymentMethod, PaymentStatus)
    WITH (FILLFACTOR = 90, PAD_INDEX = ON)
END
GO

-- 2. YENİ STORED PROCEDURE'LER
-- =====================================================

-- Çoklu branş üyelik detayları için optimize edilmiş procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetMultiBranchMemberDetails')
    DROP PROCEDURE sp_GetMultiBranchMemberDetails
GO

CREATE PROCEDURE sp_GetMultiBranchMemberDetails
    @CompanyID INT,
    @SearchText NVARCHAR(100) = NULL,
    @Gender TINYINT = NULL,
    @BranchFilter NVARCHAR(50) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Ana sorgu - çoklu branş desteği ile
    WITH MemberMemberships AS (
        SELECT 
            m.MemberID,
            m.Name,
            m.PhoneNumber,
            m.Gender,
            ms.MembershipID,
            ms.MembershipTypeID,
            mt.Branch,
            mt.TypeName,
            ms.StartDate,
            ms.EndDate,
            ms.CreationDate,
            ms.UpdatedDate,
            ms.IsFrozen,
            -- Kalan gün hesaplama
            CASE 
                WHEN ms.StartDate > GETDATE() THEN DATEDIFF(DAY, ms.StartDate, ms.EndDate)
                WHEN ms.EndDate > GETDATE() THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
                ELSE 0
            END AS RemainingDays,
            -- Gelecek başlangıç kontrolü
            CASE WHEN ms.StartDate > GETDATE() THEN 1 ELSE 0 END AS IsFutureStartDate,
            -- Satır numarası (pagination için)
            ROW_NUMBER() OVER (ORDER BY m.Name, mt.Branch) as RowNum
        FROM Members m
        INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
        INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
        WHERE 
            m.CompanyID = @CompanyID
            AND m.IsActive = 1
            AND ms.IsActive = 1
            AND ms.EndDate > GETDATE()
            AND ms.IsFrozen = 0
            AND mt.CompanyID = @CompanyID
            AND ms.CompanyID = @CompanyID
            -- Filtreleme koşulları
            AND (@SearchText IS NULL OR m.Name LIKE '%' + @SearchText + '%' OR m.PhoneNumber LIKE '%' + @SearchText + '%')
            AND (@Gender IS NULL OR m.Gender = @Gender)
            AND (@BranchFilter IS NULL OR mt.Branch = @BranchFilter)
    )
    
    -- Sonuçları döndür
    SELECT 
        MemberID,
        MembershipID,
        MembershipTypeID,
        Name,
        PhoneNumber,
        Gender,
        Branch,
        TypeName,
        StartDate,
        EndDate,
        RemainingDays,
        IsFutureStartDate,
        ISNULL(UpdatedDate, CreationDate) as UpdatedDate
    FROM MemberMemberships
    WHERE RowNum BETWEEN @Offset + 1 AND @Offset + @PageSize
    ORDER BY Name, Branch;
    
    -- Toplam kayıt sayısı
    SELECT COUNT(*) as TotalCount
    FROM Members m
    INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE 
        m.CompanyID = @CompanyID
        AND m.IsActive = 1
        AND ms.IsActive = 1
        AND ms.EndDate > GETDATE()
        AND ms.IsFrozen = 0
        AND mt.CompanyID = @CompanyID
        AND ms.CompanyID = @CompanyID
        AND (@SearchText IS NULL OR m.Name LIKE '%' + @SearchText + '%' OR m.PhoneNumber LIKE '%' + @SearchText + '%')
        AND (@Gender IS NULL OR m.Gender = @Gender)
        AND (@BranchFilter IS NULL OR mt.Branch = @BranchFilter);
END
GO

-- Branş bazlı özet bilgiler için procedure
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetBranchSummary')
    DROP PROCEDURE sp_GetBranchSummary
GO

CREATE PROCEDURE sp_GetBranchSummary
    @CompanyID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Branş bazlı üye sayıları ve toplam günler
    SELECT 
        mt.Branch,
        COUNT(DISTINCT m.MemberID) as MemberCount,
        SUM(CASE 
            WHEN ms.StartDate > GETDATE() THEN DATEDIFF(DAY, ms.StartDate, ms.EndDate)
            WHEN ms.EndDate > GETDATE() THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
            ELSE 0
        END) as TotalRemainingDays,
        AVG(CASE 
            WHEN ms.StartDate > GETDATE() THEN DATEDIFF(DAY, ms.StartDate, ms.EndDate)
            WHEN ms.EndDate > GETDATE() THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
            ELSE 0
        END) as AvgRemainingDays
    FROM Members m
    INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE 
        m.CompanyID = @CompanyID
        AND m.IsActive = 1
        AND ms.IsActive = 1
        AND ms.EndDate > GETDATE()
        AND ms.IsFrozen = 0
        AND mt.CompanyID = @CompanyID
        AND ms.CompanyID = @CompanyID
    GROUP BY mt.Branch
    ORDER BY MemberCount DESC;
END
GO

-- Üyenin tüm aktif üyeliklerini getiren procedure (silme işlemi için)
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetMemberActiveMemberships')
    DROP PROCEDURE sp_GetMemberActiveMemberships
GO

CREATE PROCEDURE sp_GetMemberActiveMemberships
    @MemberID INT,
    @CompanyID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ms.MembershipID,
        mt.Branch,
        mt.TypeName,
        ms.StartDate,
        ms.EndDate,
        CASE 
            WHEN ms.StartDate > GETDATE() THEN DATEDIFF(DAY, ms.StartDate, ms.EndDate)
            WHEN ms.EndDate > GETDATE() THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
            ELSE 0
        END AS RemainingDays,
        -- Son ödeme bilgisi
        (SELECT TOP 1 PaymentAmount 
         FROM Payments p 
         WHERE p.MemberShipID = ms.MembershipID 
           AND p.CompanyID = @CompanyID 
           AND p.IsActive = 1 
         ORDER BY p.PaymentDate DESC) as LastPaymentAmount,
        -- Ödeme durumu
        (SELECT TOP 1 PaymentStatus 
         FROM Payments p 
         WHERE p.MemberShipID = ms.MembershipID 
           AND p.CompanyID = @CompanyID 
           AND p.IsActive = 1 
         ORDER BY p.PaymentDate DESC) as PaymentStatus
    FROM Memberships ms
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE 
        ms.MemberID = @MemberID
        AND ms.CompanyID = @CompanyID
        AND ms.IsActive = 1
        AND ms.EndDate > GETDATE()
        AND mt.CompanyID = @CompanyID
    ORDER BY ms.EndDate DESC;
END
GO

-- 3. PERFORMANS İZLEME VE İSTATİSTİKLER
-- =====================================================

-- Index kullanım istatistikleri için view
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_MembershipIndexStats')
    DROP VIEW vw_MembershipIndexStats
GO

CREATE VIEW vw_MembershipIndexStats
AS
SELECT 
    i.name as IndexName,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    s.last_user_lookup,
    s.last_user_update
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
INNER JOIN sys.objects o ON i.object_id = o.object_id
WHERE o.name IN ('Memberships', 'Members', 'MembershipTypes', 'Payments')
    AND i.name IS NOT NULL;
GO

PRINT 'Çok Branşlı Üyelik Sistemi - Veritabanı güncellemeleri tamamlandı!'
PRINT 'Oluşturulan indexler:'
PRINT '- IX_Membership_Member_Type_Active_Performance'
PRINT '- IX_MembershipType_Company_Branch_Performance' 
PRINT '- IX_Member_Company_Active_Performance'
PRINT '- IX_Payment_Membership_Company_Performance'
PRINT ''
PRINT 'Oluşturulan stored procedures:'
PRINT '- sp_GetMultiBranchMemberDetails'
PRINT '- sp_GetBranchSummary'
PRINT '- sp_GetMemberActiveMemberships'
PRINT ''
PRINT 'Performans izleme:'
PRINT '- vw_MembershipIndexStats view oluşturuldu'
