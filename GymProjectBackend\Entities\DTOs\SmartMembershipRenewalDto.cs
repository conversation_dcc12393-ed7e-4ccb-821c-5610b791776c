using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Akıllı üyelik yenileme sistemi için DTO
    /// Sistem otomatik olarak yenileme mi yoksa yeni üyelik mi oluşturacağını belirler
    /// </summary>
    public class SmartMembershipRenewalDto : IDto
    {
        public int MemberID { get; set; }
        public int MembershipTypeID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Price { get; set; }
        public string PaymentMethod { get; set; }
        public int Day { get; set; }
        
        /// <summary>
        /// Yenileme stratejisi
        /// EXTEND: Mevcut üyeliği uzat (aynı branş + aynı paket)
        /// NEW: Yeni üyelik oluştur (farklı branş veya farklı paket)
        /// UPGRADE: Paket yükseltme (aynı branş, farklı paket)
        /// </summary>
        public string RenewalStrategy { get; set; }
        
        /// <summary>
        /// Eğer EXTEND ise, uzatılacak mevcut üyelik ID'si
        /// </summary>
        public int? ExistingMembershipID { get; set; }
        
        /// <summary>
        /// Kullanıcı onayı gerekli mi?
        /// Örnek: Paket downgrade durumunda onay istenir
        /// </summary>
        public bool RequiresUserConfirmation { get; set; }
        
        /// <summary>
        /// Kullanıcıya gösterilecek onay mesajı
        /// </summary>
        public string ConfirmationMessage { get; set; }
    }

    /// <summary>
    /// Üyelik yenileme analiz sonucu
    /// Frontend'e hangi seçeneklerin sunulacağını belirler
    /// </summary>
    public class MembershipRenewalAnalysisDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public int RequestedMembershipTypeID { get; set; }
        public string RequestedBranch { get; set; }
        public string RequestedTypeName { get; set; }
        
        /// <summary>
        /// Mevcut aktif üyelikler
        /// </summary>
        public List<ExistingMembershipDto> ExistingMemberships { get; set; } = new List<ExistingMembershipDto>();
        
        /// <summary>
        /// Önerilen işlem
        /// </summary>
        public RecommendedActionDto RecommendedAction { get; set; }
        
        /// <summary>
        /// Alternatif seçenekler
        /// </summary>
        public List<AlternativeActionDto> AlternativeActions { get; set; } = new List<AlternativeActionDto>();
    }

    /// <summary>
    /// Mevcut üyelik bilgisi
    /// </summary>
    public class ExistingMembershipDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public decimal LastPaymentAmount { get; set; }
        
        /// <summary>
        /// Bu üyelik ile istenen üyelik aynı branş mı?
        /// </summary>
        public bool IsSameBranch { get; set; }
        
        /// <summary>
        /// Bu üyelik ile istenen üyelik aynı paket türü mü?
        /// </summary>
        public bool IsSamePackageType { get; set; }
    }

    /// <summary>
    /// Önerilen işlem
    /// </summary>
    public class RecommendedActionDto : IDto
    {
        public string ActionType { get; set; } // EXTEND, NEW, UPGRADE, DOWNGRADE
        public string ActionDescription { get; set; }
        public string Reason { get; set; }
        public bool IsOptimal { get; set; }
        
        /// <summary>
        /// Bu işlemin sonucu
        /// </summary>
        public ActionResultPreviewDto ResultPreview { get; set; }
    }

    /// <summary>
    /// Alternatif işlem seçeneği
    /// </summary>
    public class AlternativeActionDto : IDto
    {
        public string ActionType { get; set; }
        public string ActionDescription { get; set; }
        public string Pros { get; set; } // Avantajları
        public string Cons { get; set; } // Dezavantajları
        public ActionResultPreviewDto ResultPreview { get; set; }
    }

    /// <summary>
    /// İşlem sonucu önizlemesi
    /// </summary>
    public class ActionResultPreviewDto : IDto
    {
        /// <summary>
        /// İşlem sonrası üyenin toplam aktif üyelik sayısı
        /// </summary>
        public int TotalActiveMemberships { get; set; }
        
        /// <summary>
        /// İşlem sonrası toplam kalan gün sayısı
        /// </summary>
        public int TotalRemainingDays { get; set; }
        
        /// <summary>
        /// İşlem sonrası aktif branşlar
        /// </summary>
        public List<string> ActiveBranches { get; set; } = new List<string>();
        
        /// <summary>
        /// Aylık maliyet tahmini
        /// </summary>
        public decimal EstimatedMonthlyCost { get; set; }
    }

    /// <summary>
    /// Üyelik yenileme işlem sonucu
    /// </summary>
    public class MembershipRenewalResultDto : IDto
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public string ActionTaken { get; set; }
        
        /// <summary>
        /// Oluşturulan veya güncellenen üyelik ID'si
        /// </summary>
        public int MembershipID { get; set; }
        
        /// <summary>
        /// Oluşturulan ödeme ID'si
        /// </summary>
        public int PaymentID { get; set; }
        
        /// <summary>
        /// İşlem sonrası üyenin güncel durumu
        /// </summary>
        public MemberCurrentStatusDto UpdatedMemberStatus { get; set; }
    }

    /// <summary>
    /// Üyenin güncel durumu
    /// </summary>
    public class MemberCurrentStatusDto : IDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public int TotalActiveMemberships { get; set; }
        public int TotalRemainingDays { get; set; }
        public List<string> ActiveBranches { get; set; } = new List<string>();
        public DateTime LastActivityDate { get; set; }
        public decimal TotalBalance { get; set; }
    }
}
