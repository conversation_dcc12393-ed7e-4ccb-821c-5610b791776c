/* Smart Membership Deletion Dialog Styles */

.smart-deletion-dialog {
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Dialog Header */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid var(--border-color);
}

.dialog-title {
  margin: 0;
  font-weight: 600;
  color: var(--text-primary);
}

/* Member Info Card */
.member-info-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.member-avatar .avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
}

.member-details {
  flex: 1;
}

.member-name {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: var(--text-primary);
}

.member-phone {
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.member-badges {
  display: flex;
  gap: 0.5rem;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

/* Analysis Step */
.analysis-step {
  padding: 1.5rem;
}

/* Analysis Summary */
.analysis-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.summary-content h6 {
  margin: 0 0 0.25rem 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Recommended Strategy */
.recommended-strategy {
  margin-bottom: 2rem;
}

.strategy-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.strategy-card {
  background: var(--warning-light);
  border: 1px solid var(--warning);
  border-radius: 0.5rem;
  padding: 1rem;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.strategy-type {
  background: var(--warning);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.risk-indicator {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.risk-level-1, .risk-level-2, .risk-level-3 { background: var(--success-light); color: var(--success); }
.risk-level-4, .risk-level-5, .risk-level-6 { background: var(--warning-light); color: var(--warning); }
.risk-level-7, .risk-level-8, .risk-level-9, .risk-level-10 { background: var(--danger-light); color: var(--danger); }

.strategy-description {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
  color: var(--text-primary);
}

.strategy-reasoning {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Deletion Type Selection */
.deletion-type-selection {
  margin-bottom: 2rem;
}

.selection-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.deletion-types {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.deletion-type-card {
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deletion-type-card:hover {
  border-color: var(--primary);
  background: var(--hover-bg);
}

.deletion-type-card.selected {
  border-color: var(--primary);
  background: var(--primary-light);
}

.type-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.type-label {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  cursor: pointer;
}

.type-description {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* Membership Selection */
.membership-selection {
  margin-bottom: 2rem;
}

.selected-count {
  font-size: 0.8rem;
  color: var(--primary);
  font-weight: 600;
}

.membership-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
  padding: 0.5rem;
}

.membership-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.membership-item:hover {
  border-color: var(--primary);
  background: var(--hover-bg);
}

.membership-item.selected {
  border-color: var(--primary);
  background: var(--primary-light);
}

.membership-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.membership-checkbox {
  display: flex;
  align-items: flex-start;
  padding-top: 0.25rem;
}

.membership-info {
  flex: 1;
}

.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.membership-title {
  margin: 0;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.risk-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.membership-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.risk-info {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.risk-description {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.risk-amount {
  font-size: 0.9rem;
  color: var(--text-primary);
}

.deletion-block {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--danger-light);
  border: 1px solid var(--danger);
  border-radius: 0.25rem;
  font-size: 0.85rem;
  color: var(--danger);
}

/* Warnings Section */
.warnings-section {
  margin-bottom: 2rem;
}

.warnings-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.warnings-list {
  background: var(--warning-light);
  border: 1px solid var(--warning);
  border-radius: 0.5rem;
  padding: 1rem;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--warning-dark);
}

.warning-item:last-child {
  margin-bottom: 0;
}

/* Advanced Options */
.advanced-options {
  margin-bottom: 2rem;
}

.advanced-content {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* Confirmation Step */
.confirmation-step {
  padding: 2rem 1.5rem;
  text-align: center;
}

.confirmation-header {
  margin-bottom: 2rem;
}

.confirmation-summary {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.summary-item {
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.confirmation-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--warning-light);
  border: 1px solid var(--warning);
  border-radius: 0.5rem;
}

.confirmation-checkbox input[type="checkbox"] {
  transform: scale(1.2);
}

.confirmation-checkbox label {
  margin: 0;
  font-weight: 500;
  color: var(--warning-dark);
  cursor: pointer;
}

.confirmation-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-deletion-dialog {
    max-width: 100%;
    margin: 0;
  }
  
  .analysis-summary {
    grid-template-columns: 1fr;
  }
  
  .membership-details {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .confirmation-buttons {
    flex-direction: column;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .smart-deletion-dialog {
  background: var(--dark-card-bg);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .summary-card,
[data-theme="dark"] .risk-info,
[data-theme="dark"] .advanced-content,
[data-theme="dark"] .confirmation-summary {
  background: var(--dark-card-bg);
  border-color: var(--dark-border-color);
}

[data-theme="dark"] .membership-item:hover {
  background: var(--dark-hover-bg);
}
