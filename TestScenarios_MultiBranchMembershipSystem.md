# 🧪 ÇOKLU BRANŞ ÜYELİK SİSTEMİ - TEST SENARYOLARI

## 📋 TEST PLANI ÖZETİ

### 🎯 Test Hedefleri
- ✅ Çoklu branş üyelik sistemi fonksiyonelliği
- ✅ Akıllı üyelik yenileme sistemi
- ✅ Gü<PERSON>li silme sistemi
- ✅ İki seviyeli filtreleme sistemi
- ✅ Performans ve ölçeklenebilirlik
- ✅ Geriye uyumluluk

### 🏗️ Test Ortamı
- **Backend:** .NET Core Web API
- **Frontend:** Angular 15+
- **Database:** SQL Server
- **Test Data:** 100+ salon, 10,000+ üye simülasyonu

---

## 🔄 1. ÜYELİK YENİLEME TEST SENARYOLARI

### Senaryo 1.1: Aynı Branş + Aynı Paket Yenileme (EXTEND)
```
Başlangıç Durumu:
- Üye: Ahmet YILMAZ
- Mevcut Üyelik: Fitness - 1 Aylık - 15 gün kalan
- Yeni Talep: Fitness - 1 Aylık - 30 gün

Beklenen Sonuç:
✅ Mevcut üyelik uzatılır (EndDate + 30 gün)
✅ MembershipTypeID değişmez
✅ Yeni ödeme kaydı oluşturulur
✅ Filtreleme: "1 Aylık" listesinde görünmeye devam eder

Test Adımları:
1. Üye seçimi yap
2. Aynı paket türünü seç
3. Yenileme analizi kontrol et (Strategy: EXTEND)
4. İşlemi onayla
5. Sonucu doğrula
```

### Senaryo 1.2: Aynı Branş + Farklı Paket (UPGRADE)
```
Başlangıç Durumu:
- Üye: Mehmet KAYA
- Mevcut Üyelik: Fitness - 1 Aylık - 20 gün kalan
- Yeni Talep: Fitness - 3 Aylık - 90 gün

Beklenen Sonuç:
✅ Yeni üyelik kaydı oluşturulur
✅ Eski üyelik aktif kalır
✅ Filtreleme: Hem "1 Aylık" hem "3 Aylık" listesinde görünür

Test Adımları:
1. Üye seçimi yap
2. Farklı paket türünü seç
3. Yenileme analizi kontrol et (Strategy: UPGRADE)
4. İşlemi onayla
5. Çoklu üyelik kontrolü yap
```

### Senaryo 1.3: Farklı Branş (NEW)
```
Başlangıç Durumu:
- Üye: Ayşe DEMİR
- Mevcut Üyelik: Fitness - 2 Aylık - 45 gün kalan
- Yeni Talep: Crossfit - 1 Aylık - 30 gün

Beklenen Sonuç:
✅ Yeni üyelik kaydı oluşturulur
✅ Eski üyelik etkilenmez
✅ Filtreleme: Fitness ve Crossfit listelerinde ayrı ayrı görünür

Test Adımları:
1. Üye seçimi yap
2. Farklı branş seç
3. Yenileme analizi kontrol et (Strategy: NEW)
4. İşlemi onayla
5. Çoklu branş kontrolü yap
```

---

## 🗑️ 2. AKILLI SİLME SİSTEMİ TEST SENARYOLARI

### Senaryo 2.1: Tek Üyelik Güvenli Silme
```
Başlangıç Durumu:
- Üye: Fatma ÖZKAN
- Üyelik: Pilates - 1 Aylık - 5 gün kalan - 500₺ ödeme
- Risk Seviyesi: LOW (çoğu kullanılmış)

Beklenen Sonuç:
✅ Güvenli silme onayı
✅ Risk tutarı düşük gösterilir
✅ Silme işlemi başarılı
✅ Üye listesinden kaldırılır

Test Adımları:
1. Üye seçimi yap
2. Akıllı silme dialog aç
3. Risk analizi kontrol et
4. Tek üyelik silme seç
5. Onaylayıp sil
```

### Senaryo 2.2: Çoklu Üyelik Seçici Silme
```
Başlangıç Durumu:
- Üye: Hasan YILDIZ
- Üyelikler:
  * Fitness - 3 Aylık - 80 gün kalan - 1200₺ (HIGH RISK)
  * Crossfit - 1 Aylık - 5 gün kalan - 400₺ (LOW RISK)
  * Pilates - 2 Aylık - 15 gün kalan - 800₺ (MEDIUM RISK)

Beklenen Sonuç:
✅ Risk analizi doğru hesaplanır
✅ Önerilen strateji: SELECTIVE (sadece LOW risk sil)
✅ Kullanıcı manuel seçim yapabilir
✅ Seçilen üyelikler silinir, diğerleri korunur

Test Adımları:
1. Çoklu üyelikli üye seç
2. Akıllı silme dialog aç
3. Risk analizini incele
4. Önerilen stratejiyi kontrol et
5. Manuel seçim yap
6. Silme işlemini onayla
7. Sonucu doğrula
```

### Senaryo 2.3: Yüksek Risk Uyarısı
```
Başlangıç Durumu:
- Üye: Zeynep KARA
- Üyelik: Fitness - 6 Aylık - 170 gün kalan - 2500₺ (HIGH RISK)
- Üyelik Yaşı: 10 gün (yeni alınmış)

Beklenen Sonuç:
✅ HIGH RISK uyarısı gösterilir
✅ Önerilen strateji: FREEZE (silme yerine dondurma)
✅ Finansal etki vurgulanır
✅ Kullanıcı onay vermek zorunda

Test Adımları:
1. Yüksek riskli üye seç
2. Risk uyarılarını kontrol et
3. Önerilen alternatifi incele
4. Zorla silme seçeneğini test et
5. Onay mekanizmasını doğrula
```

---

## 🔍 3. İKİ SEVİYELİ FİLTRELEME TEST SENARYOLARI

### Senaryo 3.1: Branş Özeti Görünümü (Seviye 1)
```
Test Verisi:
- Fitness: 45 üye, 2500 toplam gün
- Crossfit: 23 üye, 1200 toplam gün  
- Pilates: 18 üye, 900 toplam gün
- Yoga: 12 üye, 600 toplam gün

Beklenen Sonuç:
✅ Branş kartları doğru sayıları gösterir
✅ Toplam ve ortalama günler hesaplanır
✅ Kartlara tıklama detay görünümü açar
✅ Responsive tasarım çalışır

Test Adımları:
1. Ana filtreleme sayfasını aç
2. Çoklu branş modunu aktif et
3. Branş özetlerini kontrol et
4. Sayısal değerleri doğrula
5. Kart etkileşimlerini test et
```

### Senaryo 3.2: Paket Detay Görünümü (Seviye 2)
```
Test Verisi (Fitness Branşı):
- 1 Aylık: 15 üye, 450 toplam gün
- 3 Aylık: 20 üye, 1800 toplam gün
- 6 Aylık: 10 üye, 1200 toplam gün

Beklenen Sonuç:
✅ Fitness seçilince paket detayları görünür
✅ Her paket için doğru üye sayısı
✅ Paket seçimi üye listesini filtreler
✅ Geri dönüş butonu çalışır

Test Adımları:
1. Fitness branşını seç
2. Paket detaylarını kontrol et
3. 3 Aylık paketi seç
4. Filtrelenmiş listeyi doğrula
5. Geri dönüş işlevini test et
```

### Senaryo 3.3: Arama ve Filtreleme Kombinasyonu
```
Test Senaryosu:
- Branş: Fitness
- Paket: 3 Aylık
- Arama: "Ahmet"
- Cinsiyet: Erkek

Beklenen Sonuç:
✅ Tüm filtreler birlikte çalışır
✅ Sonuçlar doğru filtrelenir
✅ Sayfa numaralandırma çalışır
✅ Performans kabul edilebilir (<200ms)

Test Adımları:
1. Çoklu filtreleme uygula
2. Sonuçları kontrol et
3. Performansı ölç
4. Filtreleri temizle
5. Sonuçların sıfırlandığını doğrula
```

---

## ⚡ 4. PERFORMANS TEST SENARYOLARI

### Senaryo 4.1: Yük Testi (100+ Salon)
```
Test Parametreleri:
- 150 salon simülasyonu
- Salon başına ortalama 500 üye
- Toplam 75,000 üye
- Eş zamanlı 200 kullanıcı

Beklenen Sonuç:
✅ Yanıt süresi < 200ms (95. percentile)
✅ Throughput > 1000 req/sec
✅ CPU kullanımı < 70%
✅ Memory leak yok
✅ Database connection pool stabil

Test Adımları:
1. Load test ortamını hazırla
2. JMeter/Artillery ile yük oluştur
3. Performans metriklerini izle
4. Bottleneck'leri tespit et
5. Optimizasyon önerilerini uygula
```

### Senaryo 4.2: Cache Performansı
```
Test Senaryosu:
- Aynı branş özeti 1000 kez istenir
- Cache hit/miss oranları ölçülür
- Cache invalidation test edilir

Beklenen Sonuç:
✅ İlk istek: ~300ms (cache miss)
✅ Sonraki istekler: <50ms (cache hit)
✅ Cache hit oranı > 85%
✅ Cache invalidation çalışır

Test Adımları:
1. Cache'i temizle
2. İlk isteği gönder ve süreyi ölç
3. Aynı isteği tekrarla
4. Hit/miss oranlarını kontrol et
5. Cache invalidation test et
```

### Senaryo 4.3: Database Index Performansı
```
Test Sorguları:
- Çoklu branş üye listesi
- Branş özeti hesaplama
- Ödeme geçmişi sorgulama
- Üyelik yenileme analizi

Beklenen Sonuç:
✅ Index seek operations > 95%
✅ Table scan operations < 5%
✅ Query execution time < 100ms
✅ Blocking/deadlock yok

Test Adımları:
1. SQL Profiler'ı başlat
2. Test sorgularını çalıştır
3. Execution plan'ları analiz et
4. Index usage statistics kontrol et
5. Optimizasyon önerilerini uygula
```

---

## 🔄 5. GERİYE UYUMLULUK TEST SENARYOLARI

### Senaryo 5.1: Eski API Endpoint'leri
```
Test Kapsamı:
- Mevcut member filter API'si
- Eski üyelik ekleme/güncelleme
- Klasik silme işlemleri
- Raporlama endpoint'leri

Beklenen Sonuç:
✅ Eski API'ler çalışmaya devam eder
✅ Response formatları değişmez
✅ Mevcut frontend uygulamaları etkilenmez
✅ Deprecation warning'ler eklenir

Test Adımları:
1. Eski API endpoint'lerini test et
2. Response formatlarını doğrula
3. Error handling'i kontrol et
4. Deprecation mesajlarını kontrol et
5. Migration path'i dokümante et
```

### Senaryo 5.2: Mevcut Veri Uyumluluğu
```
Test Verisi:
- Eski sistemdeki mevcut üyelikler
- Geçmiş ödeme kayıtları
- Üye profil bilgileri
- Raporlama verileri

Beklenen Sonuç:
✅ Mevcut veriler bozulmaz
✅ Yeni sistem eski verileri okur
✅ Veri migration sorunsuz çalışır
✅ Backup/restore işlemleri başarılı

Test Adımları:
1. Mevcut veri backup'ı al
2. Migration script'ini çalıştır
3. Veri bütünlüğünü kontrol et
4. Eski ve yeni sistem karşılaştır
5. Rollback senaryosunu test et
```

---

## 📊 6. GERÇEK HAYAT SENARYOLARI

### Senaryo 6.1: Günlük Salon İşleyişi
```
Zaman: 09:00 - 22:00 (13 saat)
Aktiviteler:
- 150 üye girişi
- 25 yeni üyelik
- 15 üyelik yenileme
- 8 üyelik iptali
- 200+ filtreleme işlemi

Test Süreci:
1. Gerçek zamanlı simülasyon
2. Tüm işlemleri kaydet
3. Performans metriklerini izle
4. Hata durumlarını logla
5. Kullanıcı deneyimini değerlendir
```

### Senaryo 6.2: Yoğun Saatler (Peak Hours)
```
Zaman: 18:00 - 20:00 (2 saat)
Yük: Günlük trafiğin %40'ı
Eş zamanlı kullanıcı: 50+

Beklenen Sonuç:
✅ Sistem yanıt vermeye devam eder
✅ Kullanıcı deneyimi bozulmaz
✅ Auto-scaling çalışır
✅ Error rate < %1

Test Adımları:
1. Peak hour simülasyonu
2. Sistem kaynaklarını izle
3. Kullanıcı deneyimini ölç
4. Error rate'i kontrol et
5. Recovery time'ı test et
```

---

## ✅ 7. KABUL KRİTERLERİ

### Fonksiyonel Kriterler
- ✅ Tüm üyelik yenileme senaryoları çalışır
- ✅ Akıllı silme sistemi risk analizini doğru yapar
- ✅ İki seviyeli filtreleme doğru sonuçlar verir
- ✅ Çoklu branş üyeliği desteklenir
- ✅ Geriye uyumluluk korunur

### Performans Kriterler
- ✅ Yanıt süresi < 200ms (95. percentile)
- ✅ Throughput > 1000 req/sec
- ✅ Cache hit oranı > 85%
- ✅ Database query time < 100ms
- ✅ Memory usage stabil

### Güvenlik Kriterler
- ✅ Authorization kontrolleri çalışır
- ✅ Company isolation korunur
- ✅ Audit logging aktif
- ✅ Input validation yapılır
- ✅ SQL injection koruması var

### Kullanılabilirlik Kriterler
- ✅ Responsive tasarım çalışır
- ✅ Dark mode uyumlu
- ✅ Accessibility standartları
- ✅ Hata mesajları anlaşılır
- ✅ Loading states gösterilir

---

## 🚀 8. DEPLOYMENT VE CANLI TEST

### Pre-Production Test
```
Ortam: Staging
Veri: Production clone
Süre: 1 hafta
Kullanıcılar: Beta test grubu (10 salon)

Test Aktiviteleri:
1. Gerçek veri ile test
2. Kullanıcı feedback toplama
3. Performance monitoring
4. Bug fixing
5. Final optimization
```

### Production Deployment
```
Strateji: Blue-Green Deployment
Rollback Plan: Hazır
Monitoring: 7/24 aktif
Support: Dedicated team

Go-Live Checklist:
✅ Database migration tamamlandı
✅ API endpoints test edildi
✅ Frontend deployment başarılı
✅ Cache warming yapıldı
✅ Monitoring alerts aktif
✅ Support team hazır
```

---

## 📈 9. POST-DEPLOYMENT İZLEME

### İlk 24 Saat
- Real-time monitoring
- Error rate tracking
- Performance metrics
- User feedback collection
- Immediate bug fixes

### İlk Hafta
- Detailed analytics
- Performance optimization
- User training support
- Feature usage analysis
- Stability assessment

### İlk Ay
- Long-term performance trends
- Scalability assessment
- User satisfaction survey
- ROI analysis
- Future roadmap planning

---

Bu test planı, çoklu branş üyelik sisteminin tüm yönlerini kapsamlı şekilde test etmek ve 100+ salon için güvenilir bir sistem sağlamak amacıyla hazırlanmıştır.
