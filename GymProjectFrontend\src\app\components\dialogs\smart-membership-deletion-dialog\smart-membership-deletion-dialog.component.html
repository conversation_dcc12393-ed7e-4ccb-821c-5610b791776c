<div class="smart-deletion-dialog">
  <!-- <PERSON><PERSON> Header -->
  <div class="dialog-header">
    <h4 class="dialog-title">
      <i class="fas fa-shield-alt me-2 text-primary"></i>
      Akıllı Üyelik Silme <PERSON>
    </h4>
    <button 
      class="btn-close" 
      type="button" 
      (click)="onCancel()"
      [disabled]="isDeleting"
    ></button>
  </div>

  <!-- Member Info -->
  <div class="member-info-card">
    <div class="member-avatar">
      <div class="avatar-circle" [ngStyle]="{'background-color': member.gender == 1 ? 'var(--primary)' : '#FF69B4'}">
        {{ member.name.charAt(0) }}
      </div>
    </div>
    <div class="member-details">
      <h5 class="member-name">{{ member.name }}</h5>
      <p class="member-phone">{{ member.phoneNumber }}</p>
      <div class="member-badges">
        <span class="badge bg-info">{{ member.branch }}</span>
        <span class="badge bg-secondary">{{ member.typeName }}</span>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isAnalyzing" class="loading-container">
    <div class="spinner-border text-primary" role="status"></div>
    <p class="mt-3 mb-0">Üyelik analizi yapılıyor...</p>
  </div>

  <!-- Analysis Step -->
  <div *ngIf="currentStep === 'analysis' && !isAnalyzing && deletionAnalysis" class="analysis-step">
    <!-- Analysis Summary -->
    <div class="analysis-summary">
      <div class="summary-card">
        <div class="summary-icon bg-info">
          <i class="fas fa-info-circle"></i>
        </div>
        <div class="summary-content">
          <h6>Toplam Aktif Üyelik</h6>
          <span class="summary-value">{{ deletionAnalysis.totalActiveMemberships }}</span>
        </div>
      </div>
      
      <div class="summary-card">
        <div class="summary-icon bg-warning">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="summary-content">
          <h6>Toplam Risk Tutarı</h6>
          <span class="summary-value">{{ formatPrice(deletionAnalysis.totalRiskAmount) }}</span>
        </div>
      </div>
    </div>

    <!-- Recommended Strategy -->
    <div *ngIf="deletionAnalysis.recommendedStrategy" class="recommended-strategy">
      <h6 class="strategy-title">
        <i class="fas fa-lightbulb me-2 text-warning"></i>
        Önerilen Strateji
      </h6>
      <div class="strategy-card">
        <div class="strategy-header">
          <span class="strategy-type">{{ deletionAnalysis.recommendedStrategy.strategyType }}</span>
          <span class="risk-indicator" [ngClass]="'risk-level-' + deletionAnalysis.recommendedStrategy.customerSatisfactionRisk">
            Risk: {{ deletionAnalysis.recommendedStrategy.customerSatisfactionRisk }}/10
          </span>
        </div>
        <p class="strategy-description">{{ deletionAnalysis.recommendedStrategy.strategyDescription }}</p>
        <p class="strategy-reasoning">{{ deletionAnalysis.recommendedStrategy.reasoning }}</p>
      </div>
    </div>

    <!-- Deletion Type Selection -->
    <div class="deletion-type-selection">
      <h6 class="selection-title">
        <i class="fas fa-cogs me-2"></i>
        Silme Türü Seçimi
      </h6>
      
      <div class="deletion-types">
        <div 
          *ngFor="let type of deletionTypes" 
          class="deletion-type-card"
          [class.selected]="selectedDeletionType === type.value"
          (click)="selectedDeletionType = type.value; onDeletionTypeChange()"
        >
          <div class="type-header">
            <input 
              type="radio" 
              [value]="type.value" 
              [(ngModel)]="selectedDeletionType"
              (change)="onDeletionTypeChange()"
            >
            <label class="type-label">{{ type.label }}</label>
          </div>
          <p class="type-description">{{ type.description }}</p>
        </div>
      </div>
    </div>

    <!-- Membership Selection -->
    <div class="membership-selection">
      <h6 class="selection-title">
        <i class="fas fa-list-check me-2"></i>
        Üyelik Seçimi
        <span class="selected-count">({{ getSelectedCount() }}/{{ deletionAnalysis.totalActiveMemberships }})</span>
      </h6>
      
      <div class="membership-list">
        <div 
          *ngFor="let membership of deletionAnalysis.deletableMemberships" 
          class="membership-item"
          [class.selected]="isMembershipSelected(membership.membershipID)"
          [class.disabled]="!canSelectMembership(membership)"
        >
          <div class="membership-checkbox">
            <input 
              type="checkbox" 
              [checked]="isMembershipSelected(membership.membershipID)"
              [disabled]="!canSelectMembership(membership)"
              (change)="toggleMembershipSelection(membership.membershipID)"
            >
          </div>
          
          <div class="membership-info">
            <div class="membership-header">
              <h6 class="membership-title">{{ membership.branch }} - {{ membership.typeName }}</h6>
              <span class="risk-badge" [ngClass]="getRiskLevelClass(membership.riskLevel)">
                <i [class]="getRiskLevelIcon(membership.riskLevel)"></i>
                {{ membership.riskLevel }}
              </span>
            </div>
            
            <div class="membership-details">
              <div class="detail-item">
                <i class="fas fa-calendar-alt text-muted"></i>
                <span>{{ formatDate(membership.startDate) }} - {{ formatDate(membership.endDate) }}</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-clock text-muted"></i>
                <span>{{ membership.remainingDays }} gün kalan</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-money-bill text-muted"></i>
                <span>{{ formatPrice(membership.lastPaymentAmount) }}</span>
              </div>
            </div>
            
            <div class="risk-info">
              <p class="risk-description">{{ membership.riskDescription }}</p>
              <div class="risk-amount">
                Risk Tutarı: <strong>{{ formatPrice(membership.riskAmount) }}</strong>
              </div>
            </div>
            
            <div *ngIf="!membership.canBeDeleted" class="deletion-block">
              <i class="fas fa-ban text-danger"></i>
              <span>{{ membership.deletionBlockReason }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Warnings -->
    <div *ngIf="deletionAnalysis.warnings.length > 0" class="warnings-section">
      <h6 class="warnings-title">
        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
        Uyarılar
      </h6>
      <div class="warnings-list">
        <div *ngFor="let warning of deletionAnalysis.warnings" class="warning-item">
          <i class="fas fa-exclamation-circle text-warning"></i>
          <span>{{ warning }}</span>
        </div>
      </div>
    </div>

    <!-- Advanced Options -->
    <div class="advanced-options">
      <button 
        class="btn btn-outline-secondary btn-sm"
        (click)="toggleAdvancedOptions()"
      >
        <i class="fas fa-cog me-2"></i>
        Gelişmiş Seçenekler
        <i class="fas" [class.fa-chevron-down]="!showAdvancedOptions" [class.fa-chevron-up]="showAdvancedOptions"></i>
      </button>
      
      <div *ngIf="showAdvancedOptions" class="advanced-content">
        <div class="form-group">
          <label for="deletionReason">Silme Nedeni (Opsiyonel)</label>
          <textarea 
            id="deletionReason"
            class="form-control"
            [(ngModel)]="deletionReason"
            placeholder="Üyelik silme nedenini belirtiniz..."
            rows="3"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button 
        class="btn btn-secondary"
        (click)="onCancel()"
        [disabled]="isDeleting"
      >
        İptal
      </button>
      <button 
        class="btn btn-primary"
        (click)="proceedToConfirmation()"
        [disabled]="!canProceed()"
      >
        Devam Et
        <i class="fas fa-arrow-right ms-2"></i>
      </button>
    </div>
  </div>

  <!-- Confirmation Step -->
  <div *ngIf="currentStep === 'confirmation'" class="confirmation-step">
    <div class="confirmation-header">
      <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
      <h5>Silme İşlemini Onaylayın</h5>
    </div>

    <div class="confirmation-summary">
      <div class="summary-item">
        <strong>Silinecek Üyelik Sayısı:</strong> {{ getSelectedCount() }}
      </div>
      <div class="summary-item">
        <strong>Toplam Risk Tutarı:</strong> {{ formatPrice(getTotalRiskAmount()) }}
      </div>
      <div class="summary-item">
        <strong>Silme Türü:</strong> {{ selectedDeletionType }}
      </div>
    </div>

    <div class="confirmation-checkbox">
      <input 
        type="checkbox" 
        id="userConfirmation"
        [(ngModel)]="userConfirmed"
      >
      <label for="userConfirmation">
        Bu işlemin geri alınamayacağını anlıyorum ve silme işlemini onaylıyorum.
      </label>
    </div>

    <div class="confirmation-buttons">
      <button 
        class="btn btn-secondary"
        (click)="backToAnalysis()"
        [disabled]="isDeleting"
      >
        <i class="fas fa-arrow-left me-2"></i>
        Geri
      </button>
      <button 
        class="btn btn-danger"
        (click)="confirmDeletion()"
        [disabled]="!userConfirmed || isDeleting"
      >
        <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2"></span>
        <i *ngIf="!isDeleting" class="fas fa-trash-alt me-2"></i>
        {{ isDeleting ? 'Siliniyor...' : 'Sil' }}
      </button>
    </div>
  </div>
</div>
