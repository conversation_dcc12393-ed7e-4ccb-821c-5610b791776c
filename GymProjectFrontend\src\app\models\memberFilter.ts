export interface MemberFilter {
    memberID: number;
    membershipID: number;
    membershipTypeID: number;
    name: string;
    gender: number;
    phoneNumber: string;
    typeName: string;
    branch: string;
    startDate: string;
    endDate: string;
    remainingDays: number;
    isFutureStartDate: boolean; // Üyeliğin başlangıç tarihi gelecekte mi

    // Çoklu branş sistemi için eklenen yeni alanlar
    balance?: number;
    isFrozen?: boolean;
    updatedDate?: string;
    paymentStatus?: string;
    lastPaymentAmount?: number;

    // Di<PERSON><PERSON> aktif <PERSON> (çoklu branş desteği için)
    otherActiveMemberships?: OtherActiveMembership[];
    totalActiveMemberships?: number;
    totalRemainingDaysAllBranches?: number;
}

export interface OtherActiveMembership {
    membershipID: number;
    branch: string;
    typeName: string;
    remainingDays: number;
    endDate: string;
}
  
  