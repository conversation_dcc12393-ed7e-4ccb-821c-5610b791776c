using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Çoklu branş üyelik sistemi için geliştirilmiş member filter DTO
    /// Her üye için tüm aktif üyelikleri ayrı ayrı gösterir
    /// </summary>
    public class MultiBranchMemberFilterDto : IDto
    {
        public int MemberID { get; set; }
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFutureStartDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public decimal Balance { get; set; }
        
        /// <summary>
        /// Üyeliğin donmuş olup olmadığı
        /// </summary>
        public bool IsFrozen { get; set; }
        
        /// <summary>
        /// Son ödeme tutarı (silme işlemi için bilgi amaçlı)
        /// </summary>
        public decimal? LastPaymentAmount { get; set; }
        
        /// <summary>
        /// Ödeme durumu (Completed, Pending, Failed)
        /// </summary>
        public string PaymentStatus { get; set; }
    }

    /// <summary>
    /// Branş bazlı özet bilgiler için DTO
    /// İki seviyeli filtreleme sisteminin ilk seviyesi
    /// </summary>
    public class BranchSummaryDto : IDto
    {
        public string Branch { get; set; }
        public int MemberCount { get; set; }
        public int TotalRemainingDays { get; set; }
        public int AvgRemainingDays { get; set; }
        
        /// <summary>
        /// Bu branştaki paket türleri ve üye sayıları
        /// </summary>
        public List<PackageTypeCountDto> PackageTypes { get; set; } = new List<PackageTypeCountDto>();
    }

    /// <summary>
    /// Paket türü bazlı üye sayıları
    /// İki seviyeli filtreleme sisteminin ikinci seviyesi
    /// </summary>
    public class PackageTypeCountDto : IDto
    {
        public int MembershipTypeID { get; set; }
        public string TypeName { get; set; }
        public int Day { get; set; }
        public decimal Price { get; set; }
        public int MemberCount { get; set; }
        public int TotalRemainingDays { get; set; }
    }

    /// <summary>
    /// Üyenin tüm aktif üyelikleri (silme işlemi için)
    /// </summary>
    public class MemberActiveMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public List<ActiveMembershipDetailDto> ActiveMemberships { get; set; } = new List<ActiveMembershipDetailDto>();
        
        /// <summary>
        /// Toplam aktif üyelik sayısı
        /// </summary>
        public int TotalActiveMemberships => ActiveMemberships?.Count ?? 0;
        
        /// <summary>
        /// Toplam kalan gün sayısı (tüm branşlar)
        /// </summary>
        public int TotalRemainingDays => ActiveMemberships?.Sum(x => x.RemainingDays) ?? 0;
    }

    /// <summary>
    /// Aktif üyelik detayı (silme işlemi için)
    /// </summary>
    public class ActiveMembershipDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public decimal? LastPaymentAmount { get; set; }
        public string PaymentStatus { get; set; }
        
        /// <summary>
        /// Bu üyeliği silmenin risk seviyesi
        /// High: Yeni alınmış, çok para ödenmiş
        /// Medium: Orta vadeli
        /// Low: Süresi az kalmış
        /// </summary>
        public string DeletionRiskLevel { get; set; }
        
        /// <summary>
        /// Silme işlemi için uyarı mesajı
        /// </summary>
        public string DeletionWarning { get; set; }
    }
}
