import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { MultiBranchMembershipService } from '../../../services/multi-branch-membership.service';
import {
  MultiBranchMemberFilter,
  MembershipDeletionAnalysis,
  DeletableMembership,
  SmartMembershipDeletionRequest,
  DeletionType,
  RiskLevel
} from '../../../models/multi-branch-membership.models';
import { faExclamationTriangle, faTrashAlt, faShieldAlt, faInfoCircle } from '@fortawesome/free-solid-svg-icons';

export interface SmartDeletionDialogData {
  member: MultiBranchMemberFilter;
}

@Component({
  selector: 'app-smart-membership-deletion-dialog',
  templateUrl: './smart-membership-deletion-dialog.component.html',
  styleUrls: ['./smart-membership-deletion-dialog.component.css'],
  standalone: false
})
export class SmartMembershipDeletionDialogComponent implements OnInit {
  // Icons
  faExclamationTriangle = faExclamationTriangle;
  faTrashAlt = faTrashAlt;
  faShieldAlt = faShieldAlt;
  faInfoCircle = faInfoCircle;

  // Data
  member: MultiBranchMemberFilter;
  deletionAnalysis: MembershipDeletionAnalysis | null = null;
  selectedMembershipIds: number[] = [];
  deletionReason: string = '';
  userConfirmed: boolean = false;

  // UI States
  isAnalyzing: boolean = false;
  isDeleting: boolean = false;
  showAdvancedOptions: boolean = false;
  currentStep: 'analysis' | 'confirmation' | 'result' = 'analysis';

  // Deletion Types
  deletionTypes = [
    { value: DeletionType.SINGLE, label: 'Tek Üyelik Sil', description: 'Sadece seçilen üyeliği sil' },
    { value: DeletionType.MULTIPLE, label: 'Seçili Üyelikleri Sil', description: 'Seçilen üyelikleri sil' },
    { value: DeletionType.ALL, label: 'Tüm Üyelikleri Sil', description: 'Üyenin tüm aktif üyeliklerini sil' }
  ];
  selectedDeletionType: DeletionType = DeletionType.SINGLE;

  constructor(
    public dialogRef: MatDialogRef<SmartMembershipDeletionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SmartDeletionDialogData,
    private multiBranchMembershipService: MultiBranchMembershipService,
    private toastrService: ToastrService
  ) {
    this.member = data.member;
  }

  ngOnInit(): void {
    this.analyzeDeletion();
  }

  /**
   * Silme analizi yapar
   */
  analyzeDeletion(): void {
    this.isAnalyzing = true;
    this.currentStep = 'analysis';

    this.multiBranchMembershipService.analyzeMembershipDeletion(this.member.memberID).subscribe({
      next: (response) => {
        this.isAnalyzing = false;
        if (response.success && response.data) {
          this.deletionAnalysis = response.data;
          this.initializeSelection();
        } else {
          this.toastrService.error(response.message || 'Silme analizi yapılırken hata oluştu', 'Hata');
          this.dialogRef.close(false);
        }
      },
      error: (error) => {
        this.isAnalyzing = false;
        console.error('Silme analizi hatası:', error);
        this.toastrService.error('Silme analizi yapılırken bir hata oluştu', 'Hata');
        this.dialogRef.close(false);
      }
    });
  }

  /**
   * Başlangıç seçimlerini yapar
   */
  private initializeSelection(): void {
    if (!this.deletionAnalysis) return;

    // Önerilen stratejiye göre başlangıç seçimi
    if (this.deletionAnalysis.recommendedStrategy) {
      this.selectedMembershipIds = [...this.deletionAnalysis.recommendedStrategy.membershipIDsToDelete];
      
      if (this.selectedMembershipIds.length === 1) {
        this.selectedDeletionType = DeletionType.SINGLE;
      } else if (this.selectedMembershipIds.length === this.deletionAnalysis.totalActiveMemberships) {
        this.selectedDeletionType = DeletionType.ALL;
      } else {
        this.selectedDeletionType = DeletionType.MULTIPLE;
      }
    }
  }

  /**
   * Üyelik seçimini değiştirir
   */
  toggleMembershipSelection(membershipId: number): void {
    const index = this.selectedMembershipIds.indexOf(membershipId);
    if (index > -1) {
      this.selectedMembershipIds.splice(index, 1);
    } else {
      this.selectedMembershipIds.push(membershipId);
    }

    this.updateDeletionType();
  }

  /**
   * Silme türünü günceller
   */
  private updateDeletionType(): void {
    if (!this.deletionAnalysis) return;

    if (this.selectedMembershipIds.length === 0) {
      return;
    } else if (this.selectedMembershipIds.length === 1) {
      this.selectedDeletionType = DeletionType.SINGLE;
    } else if (this.selectedMembershipIds.length === this.deletionAnalysis.totalActiveMemberships) {
      this.selectedDeletionType = DeletionType.ALL;
    } else {
      this.selectedDeletionType = DeletionType.MULTIPLE;
    }
  }

  /**
   * Silme türü değiştirildiğinde
   */
  onDeletionTypeChange(): void {
    if (!this.deletionAnalysis) return;

    switch (this.selectedDeletionType) {
      case DeletionType.SINGLE:
        // İlk üyeliği seç
        this.selectedMembershipIds = this.deletionAnalysis.deletableMemberships.length > 0 
          ? [this.deletionAnalysis.deletableMemberships[0].membershipID] 
          : [];
        break;
      case DeletionType.ALL:
        // Tüm üyelikleri seç
        this.selectedMembershipIds = this.deletionAnalysis.deletableMemberships
          .filter(m => m.canBeDeleted)
          .map(m => m.membershipID);
        break;
      case DeletionType.MULTIPLE:
        // Önerilen üyelikleri seç
        this.selectedMembershipIds = this.deletionAnalysis.recommendedStrategy?.membershipIDsToDelete || [];
        break;
    }
  }

  /**
   * Onay adımına geçer
   */
  proceedToConfirmation(): void {
    if (this.selectedMembershipIds.length === 0) {
      this.toastrService.warning('Lütfen en az bir üyelik seçin', 'Uyarı');
      return;
    }

    this.currentStep = 'confirmation';
  }

  /**
   * Analiz adımına geri döner
   */
  backToAnalysis(): void {
    this.currentStep = 'analysis';
    this.userConfirmed = false;
  }

  /**
   * Silme işlemini gerçekleştirir
   */
  confirmDeletion(): void {
    if (!this.userConfirmed) {
      this.toastrService.warning('Lütfen silme işlemini onaylayın', 'Uyarı');
      return;
    }

    if (this.selectedMembershipIds.length === 0) {
      this.toastrService.warning('Silinecek üyelik seçilmemiş', 'Uyarı');
      return;
    }

    this.isDeleting = true;

    const deletionRequest: SmartMembershipDeletionRequest = {
      memberID: this.member.memberID,
      membershipIDsToDelete: this.selectedMembershipIds,
      deletionType: this.selectedDeletionType,
      userConfirmed: this.userConfirmed,
      deletionReason: this.deletionReason || undefined
    };

    this.multiBranchMembershipService.processSmartMembershipDeletion(deletionRequest).subscribe({
      next: (response) => {
        this.isDeleting = false;
        if (response.success) {
          this.toastrService.success('Üyelik silme işlemi başarıyla tamamlandı', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'Silme işlemi başarısız', 'Hata');
        }
      },
      error: (error) => {
        this.isDeleting = false;
        console.error('Silme işlemi hatası:', error);
        this.toastrService.error('Silme işlemi sırasında bir hata oluştu', 'Hata');
      }
    });
  }

  /**
   * Dialog'u kapatır
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Risk seviyesine göre CSS sınıfı döndürür
   */
  getRiskLevelClass(riskLevel: string): string {
    return this.multiBranchMembershipService.getRiskLevelClass(riskLevel);
  }

  /**
   * Risk seviyesine göre ikon döndürür
   */
  getRiskLevelIcon(riskLevel: string): string {
    switch (riskLevel?.toUpperCase()) {
      case 'HIGH':
        return 'fas fa-exclamation-triangle text-danger';
      case 'MEDIUM':
        return 'fas fa-exclamation-circle text-warning';
      case 'LOW':
        return 'fas fa-check-circle text-success';
      default:
        return 'fas fa-question-circle text-secondary';
    }
  }

  /**
   * Fiyatı formatlar
   */
  formatPrice(price: number): string {
    return this.multiBranchMembershipService.formatPrice(price);
  }

  /**
   * Tarihi formatlar
   */
  formatDate(date: string): string {
    return this.multiBranchMembershipService.formatDate(date);
  }

  /**
   * Seçilen üyeliklerin toplam risk tutarını hesaplar
   */
  getTotalRiskAmount(): number {
    if (!this.deletionAnalysis) return 0;
    
    return this.deletionAnalysis.deletableMemberships
      .filter(m => this.selectedMembershipIds.includes(m.membershipID))
      .reduce((total, m) => total + m.riskAmount, 0);
  }

  /**
   * Seçilen üyeliklerin sayısını döndürür
   */
  getSelectedCount(): number {
    return this.selectedMembershipIds.length;
  }

  /**
   * Üyeliğin seçili olup olmadığını kontrol eder
   */
  isMembershipSelected(membershipId: number): boolean {
    return this.selectedMembershipIds.includes(membershipId);
  }

  /**
   * Üyeliğin seçilebilir olup olmadığını kontrol eder
   */
  canSelectMembership(membership: DeletableMembership): boolean {
    return membership.canBeDeleted;
  }

  /**
   * Silme işleminin yapılabilir olup olmadığını kontrol eder
   */
  canProceed(): boolean {
    return this.selectedMembershipIds.length > 0 && !this.isDeleting;
  }

  /**
   * Gelişmiş seçenekleri gösterir/gizler
   */
  toggleAdvancedOptions(): void {
    this.showAdvancedOptions = !this.showAdvancedOptions;
  }
}
