import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { MultiBranchMembershipService } from '../../services/multi-branch-membership.service';
import { MembershipType } from '../../models/membershipType';
import { MembershipTypeService } from '../../services/membership-type.service';
import { MatDialog } from '@angular/material/dialog';
import { MembershipUpdateComponent } from '../crud/membership-update/membership-update.component';
import { SmartMembershipDeletionDialogComponent } from '../dialogs/smart-membership-deletion-dialog/smart-membership-deletion-dialog.component';
import { faEdit, faSnowflake, faTrashAlt, faFilter, faEye, faList } from '@fortawesome/free-solid-svg-icons';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberFilter } from '../../models/memberFilter';
import {
  MultiBranchMemberFilter,
  BranchSummary,
  PackageTypeCount,
  TwoLevelFilterState,
  MultiBranchFilterParams
} from '../../models/multi-branch-membership.models';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { FreezeMembershipDialogComponent } from '../freeze-membership-dialog/freeze-membership-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';

@Component({
    selector: 'app-member-filter',
    templateUrl: './member-filter.component.html',
    styleUrls: ['./member-filter.component.css'],
    standalone: false
})
export class MemberFilterComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('genderChart') genderChartRef: ElementRef;
  genderChart: Chart;

  // Mevcut sistem (geriye uyumluluk için)
  members: MemberFilter[] = [];
  activeMembersCount: number = 0;
  memberFilterText: string = '';
  private searchTextSubject = new Subject<string>();
  genderFilter: string = '';
  branchFilter: string = '';
  membershipTypes: MembershipType[] = [];

  // Çoklu branş sistemi
  multiBranchMembers: MultiBranchMemberFilter[] = [];
  branchSummaries: BranchSummary[] = [];
  selectedBranchPackages: PackageTypeCount[] = [];

  // İki seviyeli filtreleme state
  filterState: TwoLevelFilterState = {
    selectedBranch: undefined,
    selectedPackageType: undefined,
    isDetailView: false,
    branchSummaries: [],
    filteredMembers: []
  };

  // UI kontrolleri
  isMultiBranchMode: boolean = true; // Yeni sistem varsayılan
  showBranchSummary: boolean = true;
  showPackageDetails: boolean = false;

  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faSnowflake = faSnowflake;
  faFilter = faFilter;
  faEye = faEye;
  faList = faList;

  // Loading states
  isLoading: boolean = false;
  isBranchSummaryLoading: boolean = false;
  isPackageDetailsLoading: boolean = false;

  // Pagination
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalActiveMembers: number = 0;

  genderCounts = {
    all: 0,
    male: 0,
    female: 0
  };
  branchCounts: { [key: string]: number } = {};

  constructor(
    private memberService: MemberService,
    private multiBranchMembershipService: MultiBranchMembershipService,
    private membershipTypeService: MembershipTypeService,
    private membershipService: MembershipService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService
  ) {
    this.searchTextSubject.pipe(
      debounceTime(750),
      distinctUntilChanged()
    ).subscribe(searchText => {
      this.memberFilterText = searchText;
      this.currentPage = 1;
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    this.getBranches();

    if (this.isMultiBranchMode) {
      this.loadBranchSummary();
      this.loadMultiBranchMembers();
    } else {
      this.loadMembers();
    }

    this.getTotalActiveMembers();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initGenderChart();
    }, 500);
  }

  ngOnDestroy(): void {
    this.searchTextSubject.complete();
    if (this.genderChart) {
      this.genderChart.destroy();
    }
  }

  initGenderChart(): void {
    const canvas = document.getElementById('genderChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    if (this.genderChart) {
      this.genderChart.destroy();
    }

    const chartData = {
      labels: ['Erkek', 'Kadın'],
      datasets: [{
        data: [this.genderCounts.male, this.genderCounts.female],
      backgroundColor: [
        'rgba(67, 97, 238, 0.7)',  // Erkek - Mavi
        'rgba(255, 105, 180, 0.7)'   // Kadın - Pembe
      ],
      borderColor: [
        'rgb(67, 97, 238)',
        'rgb(255, 105, 180)'
        ],
        borderWidth: 1,
        hoverOffset: 4
      }]
    };

    const config: ChartConfiguration = {
      type: 'doughnut' as ChartType,
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle',
              font: {
                size: 12
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw as number;
                const total = (context.dataset.data as number[]).reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              }
            }
          }
        },
        animation: {
          duration: 1000
        },
        layout: {
          padding: {
            top: 10,
            bottom: 20
          }
        }
      }
    };

    this.genderChart = new Chart(ctx, config);
  }

  searchTextChanged(text: string) {
    this.searchTextSubject.next(text);
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.loadMembers();
    this.getTotalActiveMembers();
  }
  openFreezeDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(FreezeMembershipDialogComponent, {
      width: '400px',
      data: { 
        memberName: member.name,
        membershipID: member.membershipID
      }
    });

    dialogRef.afterClosed().subscribe(freezeDays => {
      if (freezeDays) {
        this.membershipService.freezeMembership(member.membershipID, freezeDays).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üyelik başarıyla donduruldu');
              this.loadMembers();
            } else {
              this.toastrService.error(response.message);
            }
          },
          error: (error) => {
            this.toastrService.error('Üyelik dondurulurken bir hata oluştu');
          }
        });
      }
    });
  }
  getTotalActiveMembers() {
    this.memberService.getTotalActiveMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalActiveMembers = response.data;
          this.activeMembersCount = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total members:', error);
      },
    });
  }

  loadMembers() {
    this.isLoading = true;
    const gender = this.genderFilter ? parseInt(this.genderFilter) : undefined;

    this.memberService
      .getMemberDetailsPaginated(
        this.currentPage,
        this.memberFilterText,
        gender,
        this.branchFilter
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.members = response.data.data;
            this.totalPages = response.data.totalPages;
            this.totalItems = response.data.totalCount;
            this.calculateActiveMembersCount();
            this.calculateFilterCounts();
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching members:', error);
          this.toastrService.error(
            'Üyeler yüklenirken bir hata oluştu.',
            'Hata'
          );
          this.isLoading = false;
        },
      });
  }

  calculateFilterCounts() {
    this.genderCounts.all = this.totalItems;
  
    this.memberService.getActiveMemberCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.genderCounts.male = response.data['male'];
          this.genderCounts.female = response.data['female'];
          
          // Cinsiyet grafiğini güncelle
          if (this.genderChart) {
            this.genderChart.data.datasets[0].data = [
              this.genderCounts.male, 
              this.genderCounts.female
            ];
            this.genderChart.update();
          } else {
            this.initGenderChart();
          }
        }
      },
      error: (error) => {
        console.error('Error fetching gender counts:', error);
      }
    });
  
    this.memberService.getBranchCounts().subscribe({
      next: (response) => {
        if (response.success) {
          this.branchCounts = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching branch counts:', error);
      }
    });
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadMembers();
    }
  }

  getBranches() {
    this.membershipTypeService.getMembershipTypes().subscribe((response) => {
      this.membershipTypes = this.getUniqueBranches(response.data);
    });
  }

  getUniqueBranches(membershipTypes: MembershipType[]): MembershipType[] {
    const uniqueBranches: MembershipType[] = [];
    const branchMap = new Map<string, boolean>();

    membershipTypes.forEach((type) => {
      if (!branchMap.has(type.branch)) {
        branchMap.set(type.branch, true);
        uniqueBranches.push(type);
      }
    });

    return uniqueBranches;
  }

  calculateActiveMembersCount() {
    if (this.isMultiBranchMode) {
      this.activeMembersCount = this.multiBranchMembers.length;
    } else {
      this.activeMembersCount = this.members.filter((member) => {
        return member.remainingDays >= 0;
      }).length;
    }
  }

  openUpdateDialog(member: MemberFilter): void {
    const dialogRef = this.dialog.open(MembershipUpdateComponent, {
      width: '400px',
      data: {
        membershipID: member.membershipID,
        memberID: member.memberID,
        membershipTypeID: member.membershipTypeID,
        startDate: member.startDate,
        endDate: member.endDate,
        name: member.name,
        branch: member.branch,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  deleteMember(member: MemberFilter) {
    this.dialogService.confirmMembershipDelete(member.name, member).subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.membershipService.delete(member.membershipID).subscribe(
          (response) => {
            this.isLoading = false;
            if (response.success) {
              this.toastrService.success(response.message, 'Başarılı');
              this.loadMembers();
              this.getTotalActiveMembers();
            } else {
              this.toastrService.error(response.message, 'Hata');
            }
          },
          (error) => {
            this.isLoading = false;
            this.toastrService.error('Üye silinirken bir hata oluştu.', 'Hata');
          }
        );
      }
    });
  }

  // =====================================================
  // Çoklu Branş Sistemi Metodları
  // =====================================================

  /**
   * Branş özetlerini yükler (İki seviyeli filtreleme - Seviye 1)
   */
  loadBranchSummary(): void {
    this.isBranchSummaryLoading = true;

    this.multiBranchMembershipService.getBranchSummary().subscribe({
      next: (response) => {
        this.isBranchSummaryLoading = false;
        if (response.success && response.data) {
          this.branchSummaries = response.data;
          this.filterState.branchSummaries = response.data;
          this.calculateBranchCounts();
        } else {
          this.toastrService.error(response.message || 'Branş özeti yüklenirken hata oluştu', 'Hata');
        }
      },
      error: (error) => {
        this.isBranchSummaryLoading = false;
        console.error('Branş özeti yükleme hatası:', error);
        this.toastrService.error('Branş özeti yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  /**
   * Çoklu branş üye listesini yükler
   */
  loadMultiBranchMembers(): void {
    this.isLoading = true;

    const params: MultiBranchFilterParams = {
      searchText: this.memberFilterText || undefined,
      gender: this.genderFilter ? parseInt(this.genderFilter) : undefined,
      branchFilter: this.branchFilter || undefined,
      pageNumber: this.currentPage,
      pageSize: 50
    };

    this.multiBranchMembershipService.getMultiBranchMemberDetails(params).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success && response.data) {
          this.multiBranchMembers = response.data;
          this.filterState.filteredMembers = response.data;
          this.totalItems = response.totalCount || 0;
          this.calculateActiveMembersCount();
        } else {
          this.toastrService.error(response.message || 'Üyeler yüklenirken hata oluştu', 'Hata');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Çoklu branş üye listesi yükleme hatası:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  /**
   * Belirli bir branşın paket detaylarını yükler (İki seviyeli filtreleme - Seviye 2)
   */
  loadPackageDetails(branch: string): void {
    this.isPackageDetailsLoading = true;
    this.filterState.selectedBranch = branch;

    this.multiBranchMembershipService.getPackageTypesByBranch(branch).subscribe({
      next: (response) => {
        this.isPackageDetailsLoading = false;
        if (response.success && response.data) {
          this.selectedBranchPackages = response.data;
          this.showPackageDetails = true;
          this.filterState.isDetailView = true;
        } else {
          this.toastrService.error(response.message || 'Paket detayları yüklenirken hata oluştu', 'Hata');
        }
      },
      error: (error) => {
        this.isPackageDetailsLoading = false;
        console.error('Paket detayları yükleme hatası:', error);
        this.toastrService.error('Paket detayları yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  /**
   * Branş seçimi yapar ve detay görünümüne geçer
   */
  selectBranch(branch: string): void {
    this.filterState.selectedBranch = branch;
    this.branchFilter = branch;
    this.loadPackageDetails(branch);
    this.loadMultiBranchMembers(); // Seçilen branşa göre üyeleri filtrele
  }

  /**
   * Paket türü seçimi yapar
   */
  selectPackageType(packageTypeID: number): void {
    this.filterState.selectedPackageType = packageTypeID;
    // Burada belirli paket türüne göre filtreleme yapılabilir
    this.loadMultiBranchMembers();
  }

  /**
   * Genel görünüme geri döner (Seviye 1)
   */
  backToBranchSummary(): void {
    this.filterState.selectedBranch = undefined;
    this.filterState.selectedPackageType = undefined;
    this.filterState.isDetailView = false;
    this.showPackageDetails = false;
    this.branchFilter = '';
    this.loadBranchSummary();
    this.loadMultiBranchMembers();
  }

  /**
   * Görünüm modunu değiştirir (eski sistem / yeni sistem)
   */
  toggleViewMode(): void {
    this.isMultiBranchMode = !this.isMultiBranchMode;

    if (this.isMultiBranchMode) {
      this.loadBranchSummary();
      this.loadMultiBranchMembers();
    } else {
      this.loadMembers();
    }
  }

  /**
   * Branş sayılarını hesaplar
   */
  private calculateBranchCounts(): void {
    this.branchCounts = {};
    this.branchSummaries.forEach(branch => {
      this.branchCounts[branch.branch] = branch.memberCount;
    });
  }



  /**
   * Risk seviyesine göre CSS sınıfı döndürür
   */
  getRiskLevelClass(riskLevel: string): string {
    return this.multiBranchMembershipService.getRiskLevelClass(riskLevel);
  }

  /**
   * Ödeme durumuna göre CSS sınıfı döndürür
   */
  getPaymentStatusClass(paymentStatus: string): string {
    return this.multiBranchMembershipService.getPaymentStatusClass(paymentStatus);
  }

  /**
   * Kalan gün sayısına göre renk sınıfı döndürür
   */
  getRemainingDaysClass(remainingDays: number): string {
    return this.multiBranchMembershipService.getRemainingDaysClass(remainingDays);
  }

  /**
   * Fiyatı formatlar
   */
  formatPrice(price: number): string {
    return this.multiBranchMembershipService.formatPrice(price);
  }

  /**
   * Tarihi formatlar
   */
  formatDate(date: string): string {
    return this.multiBranchMembershipService.formatDate(date);
  }

  /**
   * Akıllı silme dialog'unu açar
   */
  openSmartDeleteDialog(member: MultiBranchMemberFilter): void {
    const dialogRef = this.dialog.open(SmartMembershipDeletionDialogComponent, {
      width: '800px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: true,
      data: { member: member }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Silme işlemi başarılı, listeyi yenile
        if (this.isMultiBranchMode) {
          this.loadMultiBranchMembers();
          this.loadBranchSummary();
        } else {
          this.loadMembers();
        }
        this.getTotalActiveMembers();
      }
    });
  }

  /**
   * Akıllı yenileme dialog'unu açar
   */
  openRenewalDialog(member: MultiBranchMemberFilter): void {
    // Bu metod akıllı yenileme sistemi component'i oluşturulduktan sonra implementasyona alınacak
    console.log('Akıllı yenileme dialog açılacak:', member);
    this.toastrService.info('Akıllı yenileme sistemi yakında aktif olacak', 'Bilgi');
  }
}