using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Çoklu branş üyelik yönetimi API Controller
    /// Akıll<PERSON> yenileme, gü<PERSON>li silme ve iki seviyeli filtreleme işlemlerini yönetir
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    [EnableRateLimiting("DefaultPolicy")]
    public class MultiBranchMembershipController : ControllerBase
    {
        private readonly IMultiBranchMembershipService _multiBranchMembershipService;

        public MultiBranchMembershipController(IMultiBranchMembershipService multiBranchMembershipService)
        {
            _multiBranchMembershipService = multiBranchMembershipService;
        }

        #region Çoklu Branş Filtreleme API'leri

        /// <summary>
        /// Çoklu branş desteği ile üye listesini getirir
        /// Her üye için tüm aktif üyelikleri ayrı ayrı gösterir
        /// </summary>
        /// <param name="searchText">Arama metni (ad, telefon)</param>
        /// <param name="gender">Cinsiyet filtresi (1: Erkek, 2: Kadın)</param>
        /// <param name="branchFilter">Branş filtresi</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <returns>Çoklu branş üye listesi</returns>
        [HttpGet("getmultibranch")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult GetMultiBranchMemberDetails(
            [FromQuery] string searchText = null,
            [FromQuery] int? gender = null,
            [FromQuery] string branchFilter = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var result = _multiBranchMembershipService.GetMultiBranchMemberDetails(
                    searchText, gender, branchFilter, pageNumber, pageSize);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data,
                        totalCount = result.Data?.Count ?? 0
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Branş bazlı özet bilgileri getirir (İki seviyeli filtreleme - Seviye 1)
        /// </summary>
        /// <returns>Branş özet listesi</returns>
        [HttpGet("getbranchsummary")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult GetBranchSummary()
        {
            try
            {
                var result = _multiBranchMembershipService.GetBranchSummary();

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Belirli bir branştaki paket türlerini ve üye sayılarını getirir (İki seviyeli filtreleme - Seviye 2)
        /// </summary>
        /// <param name="branch">Branş adı</param>
        /// <returns>Paket türü listesi</returns>
        [HttpGet("getpackagetypes/{branch}")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult GetPackageTypesByBranch(string branch)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(branch))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Branş adı boş olamaz"
                    });
                }

                var result = _multiBranchMembershipService.GetPackageTypesByBranch(branch);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data,
                        branch = branch
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Üyenin tüm aktif üyeliklerini getirir (silme işlemi için)
        /// </summary>
        /// <param name="memberID">Üye ID'si</param>
        /// <returns>Üyenin aktif üyelikleri</returns>
        [HttpGet("getactivememberships/{memberID}")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult GetMemberActiveMemberships(int memberID)
        {
            try
            {
                if (memberID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si"
                    });
                }

                var result = _multiBranchMembershipService.GetMemberActiveMemberships(memberID);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        #endregion

        #region Akıllı Üyelik Yenileme API'leri

        /// <summary>
        /// Üyelik yenileme analizi yapar
        /// Sistem otomatik olarak yenileme mi yoksa yeni üyelik mi oluşturacağını belirler
        /// </summary>
        /// <param name="memberID">Üye ID'si</param>
        /// <param name="membershipTypeID">Üyelik türü ID'si</param>
        /// <returns>Yenileme analiz sonucu</returns>
        [HttpGet("analyzerenewal")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult AnalyzeMembershipRenewal([FromQuery] int memberID, [FromQuery] int membershipTypeID)
        {
            try
            {
                if (memberID <= 0 || membershipTypeID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si veya üyelik türü ID'si"
                    });
                }

                var result = _multiBranchMembershipService.AnalyzeMembershipRenewal(memberID, membershipTypeID);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Akıllı üyelik yenileme işlemini gerçekleştirir
        /// </summary>
        /// <param name="renewalDto">Yenileme bilgileri</param>
        /// <returns>İşlem sonucu</returns>
        [HttpPost("smartrenewal")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult ProcessSmartMembershipRenewal([FromBody] SmartMembershipRenewalDto renewalDto)
        {
            try
            {
                if (renewalDto == null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Yenileme bilgileri boş olamaz"
                    });
                }

                // Temel validasyonlar
                if (renewalDto.MemberID <= 0 || renewalDto.MembershipTypeID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si veya üyelik türü ID'si"
                    });
                }

                if (renewalDto.Price <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz fiyat bilgisi"
                    });
                }

                var result = _multiBranchMembershipService.ProcessSmartMembershipRenewal(renewalDto);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Üyelik yenileme stratejisini belirler
        /// </summary>
        /// <param name="memberID">Üye ID'si</param>
        /// <param name="membershipTypeID">Üyelik türü ID'si</param>
        /// <returns>Yenileme stratejisi</returns>
        [HttpGet("renewalstrategy")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult DetermineRenewalStrategy([FromQuery] int memberID, [FromQuery] int membershipTypeID)
        {
            try
            {
                if (memberID <= 0 || membershipTypeID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si veya üyelik türü ID'si"
                    });
                }

                var result = _multiBranchMembershipService.DetermineRenewalStrategy(memberID, membershipTypeID);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        strategy = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        #endregion

        #region Akıllı Silme API'leri

        /// <summary>
        /// Silme işlemi öncesi analiz yapar
        /// Hangi üyeliklerin silinebileceğini ve risklerini belirler
        /// </summary>
        /// <param name="memberID">Üye ID'si</param>
        /// <returns>Silme analiz sonucu</returns>
        [HttpGet("analyzedeletion/{memberID}")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult AnalyzeMembershipDeletion(int memberID)
        {
            try
            {
                if (memberID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si"
                    });
                }

                var result = _multiBranchMembershipService.AnalyzeMembershipDeletion(memberID);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Akıllı silme işlemini gerçekleştirir
        /// Çoklu üyelikleri güvenli şekilde yönetir
        /// </summary>
        /// <param name="deletionRequest">Silme isteği bilgileri</param>
        /// <returns>Silme işlem sonucu</returns>
        [HttpPost("smartdeletion")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult ProcessSmartMembershipDeletion([FromBody] SmartMembershipDeletionRequestDto deletionRequest)
        {
            try
            {
                if (deletionRequest == null)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Silme isteği bilgileri boş olamaz"
                    });
                }

                if (deletionRequest.MemberID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si"
                    });
                }

                if (!deletionRequest.UserConfirmed)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Kullanıcı onayı gerekli"
                    });
                }

                var result = _multiBranchMembershipService.ProcessSmartMembershipDeletion(deletionRequest);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Tek üyelik silme işlemi (geriye uyumluluk için)
        /// </summary>
        /// <param name="membershipID">Üyelik ID'si</param>
        /// <param name="deletionReason">Silme nedeni</param>
        /// <returns>Silme işlem sonucu</returns>
        [HttpDelete("deletesingle/{membershipID}")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult DeleteSingleMembership(int membershipID, [FromQuery] string deletionReason = null)
        {
            try
            {
                if (membershipID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üyelik ID'si"
                    });
                }

                var result = _multiBranchMembershipService.DeleteSingleMembership(membershipID, deletionReason);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Üyenin tüm üyeliklerini siler
        /// </summary>
        /// <param name="memberID">Üye ID'si</param>
        /// <param name="deletionReason">Silme nedeni</param>
        /// <returns>Silme işlem sonucu</returns>
        [HttpDelete("deleteall/{memberID}")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult DeleteAllMemberships(int memberID, [FromQuery] string deletionReason = null)
        {
            try
            {
                if (memberID <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Geçersiz üye ID'si"
                    });
                }

                var result = _multiBranchMembershipService.DeleteAllMemberships(memberID, deletionReason);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        #endregion

        #region Yardımcı API'ler

        /// <summary>
        /// Cache'i temizler
        /// </summary>
        /// <returns>İşlem sonucu</returns>
        [HttpPost("clearcache")]
        [Authorize(Roles = "owner")]
        public IActionResult ClearCache()
        {
            try
            {
                var result = _multiBranchMembershipService.ClearCache();

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Sistem performans metriklerini getirir
        /// </summary>
        /// <returns>Performans metrikleri</returns>
        [HttpGet("performancemetrics")]
        [Authorize(Roles = "owner,admin")]
        public IActionResult GetPerformanceMetrics()
        {
            try
            {
                var result = _multiBranchMembershipService.GetPerformanceMetrics();

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }

                return BadRequest(new
                {
                    success = false,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Sunucu hatası: " + ex.Message
                });
            }
        }

        #endregion
    }
}
