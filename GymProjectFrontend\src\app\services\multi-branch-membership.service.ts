import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import {
  MultiBranchMemberFilter,
  BranchSummary,
  PackageTypeCount,
  MemberActiveMemberships,
  SmartMembershipRenewal,
  MembershipRenewalAnalysis,
  SmartMembershipDeletionRequest,
  MembershipDeletionAnalysis,
  MembershipDeletionResult,
  MemberCurrentStatus,
  PerformanceMetrics,
  MultiBranchApiResponse,
  PaginatedMultiBranchResponse,
  MultiBranchFilterParams
} from '../models/multi-branch-membership.models';

@Injectable({
  providedIn: 'root'
})
export class MultiBranchMembershipService {
  private apiUrl = environment.apiUrl + '/multibranch-membership';

  constructor(private httpClient: HttpClient) { }

  // =====================================================
  // Çoklu Branş Filtreleme API'leri
  // =====================================================

  /**
   * Çoklu branş desteği ile üye listesini getirir
   * Her üye için tüm aktif üyelikleri ayrı ayrı gösterir
   */
  getMultiBranchMemberDetails(params: MultiBranchFilterParams): Observable<MultiBranchApiResponse<MultiBranchMemberFilter[]>> {
    let httpParams = new HttpParams();
    
    if (params.searchText) {
      httpParams = httpParams.set('searchText', params.searchText);
    }
    if (params.gender !== undefined && params.gender !== null) {
      httpParams = httpParams.set('gender', params.gender.toString());
    }
    if (params.branchFilter) {
      httpParams = httpParams.set('branchFilter', params.branchFilter);
    }
    if (params.pageNumber) {
      httpParams = httpParams.set('pageNumber', params.pageNumber.toString());
    }
    if (params.pageSize) {
      httpParams = httpParams.set('pageSize', params.pageSize.toString());
    }

    return this.httpClient.get<MultiBranchApiResponse<MultiBranchMemberFilter[]>>(
      `${this.apiUrl}/getmultibranch`, 
      { params: httpParams }
    );
  }

  /**
   * Branş bazlı özet bilgileri getirir (İki seviyeli filtreleme - Seviye 1)
   */
  getBranchSummary(): Observable<MultiBranchApiResponse<BranchSummary[]>> {
    return this.httpClient.get<MultiBranchApiResponse<BranchSummary[]>>(
      `${this.apiUrl}/getbranchsummary`
    );
  }

  /**
   * Belirli bir branştaki paket türlerini ve üye sayılarını getirir (İki seviyeli filtreleme - Seviye 2)
   */
  getPackageTypesByBranch(branch: string): Observable<MultiBranchApiResponse<PackageTypeCount[]>> {
    return this.httpClient.get<MultiBranchApiResponse<PackageTypeCount[]>>(
      `${this.apiUrl}/getpackagetypes/${encodeURIComponent(branch)}`
    );
  }

  /**
   * Üyenin tüm aktif üyeliklerini getirir (silme işlemi için)
   */
  getMemberActiveMemberships(memberID: number): Observable<MultiBranchApiResponse<MemberActiveMemberships>> {
    return this.httpClient.get<MultiBranchApiResponse<MemberActiveMemberships>>(
      `${this.apiUrl}/getactivememberships/${memberID}`
    );
  }

  // =====================================================
  // Akıllı Üyelik Yenileme API'leri
  // =====================================================

  /**
   * Üyelik yenileme analizi yapar
   * Sistem otomatik olarak yenileme mi yoksa yeni üyelik mi oluşturacağını belirler
   */
  analyzeMembershipRenewal(memberID: number, membershipTypeID: number): Observable<MultiBranchApiResponse<MembershipRenewalAnalysis>> {
    const params = new HttpParams()
      .set('memberID', memberID.toString())
      .set('membershipTypeID', membershipTypeID.toString());

    return this.httpClient.get<MultiBranchApiResponse<MembershipRenewalAnalysis>>(
      `${this.apiUrl}/analyzerenewal`,
      { params }
    );
  }

  /**
   * Akıllı üyelik yenileme işlemini gerçekleştirir
   */
  processSmartMembershipRenewal(renewalData: SmartMembershipRenewal): Observable<MultiBranchApiResponse<any>> {
    return this.httpClient.post<MultiBranchApiResponse<any>>(
      `${this.apiUrl}/smartrenewal`,
      renewalData
    );
  }

  /**
   * Üyelik yenileme stratejisini belirler
   */
  determineRenewalStrategy(memberID: number, membershipTypeID: number): Observable<MultiBranchApiResponse<string>> {
    const params = new HttpParams()
      .set('memberID', memberID.toString())
      .set('membershipTypeID', membershipTypeID.toString());

    return this.httpClient.get<MultiBranchApiResponse<string>>(
      `${this.apiUrl}/renewalstrategy`,
      { params }
    );
  }

  // =====================================================
  // Akıllı Silme API'leri
  // =====================================================

  /**
   * Silme işlemi öncesi analiz yapar
   * Hangi üyeliklerin silinebileceğini ve risklerini belirler
   */
  analyzeMembershipDeletion(memberID: number): Observable<MultiBranchApiResponse<MembershipDeletionAnalysis>> {
    return this.httpClient.get<MultiBranchApiResponse<MembershipDeletionAnalysis>>(
      `${this.apiUrl}/analyzedeletion/${memberID}`
    );
  }

  /**
   * Akıllı silme işlemini gerçekleştirir
   * Çoklu üyelikleri güvenli şekilde yönetir
   */
  processSmartMembershipDeletion(deletionRequest: SmartMembershipDeletionRequest): Observable<MultiBranchApiResponse<MembershipDeletionResult>> {
    return this.httpClient.post<MultiBranchApiResponse<MembershipDeletionResult>>(
      `${this.apiUrl}/smartdeletion`,
      deletionRequest
    );
  }

  /**
   * Tek üyelik silme işlemi (geriye uyumluluk için)
   */
  deleteSingleMembership(membershipID: number, deletionReason?: string): Observable<MultiBranchApiResponse<any>> {
    let params = new HttpParams();
    if (deletionReason) {
      params = params.set('deletionReason', deletionReason);
    }

    return this.httpClient.delete<MultiBranchApiResponse<any>>(
      `${this.apiUrl}/deletesingle/${membershipID}`,
      { params }
    );
  }

  /**
   * Üyenin tüm üyeliklerini siler
   */
  deleteAllMemberships(memberID: number, deletionReason?: string): Observable<MultiBranchApiResponse<any>> {
    let params = new HttpParams();
    if (deletionReason) {
      params = params.set('deletionReason', deletionReason);
    }

    return this.httpClient.delete<MultiBranchApiResponse<any>>(
      `${this.apiUrl}/deleteall/${memberID}`,
      { params }
    );
  }

  // =====================================================
  // Yardımcı API'ler
  // =====================================================

  /**
   * Cache'i temizler
   */
  clearCache(): Observable<MultiBranchApiResponse<any>> {
    return this.httpClient.post<MultiBranchApiResponse<any>>(
      `${this.apiUrl}/clearcache`,
      {}
    );
  }

  /**
   * Sistem performans metriklerini getirir
   */
  getPerformanceMetrics(): Observable<MultiBranchApiResponse<PerformanceMetrics>> {
    return this.httpClient.get<MultiBranchApiResponse<PerformanceMetrics>>(
      `${this.apiUrl}/performancemetrics`
    );
  }

  // =====================================================
  // Yardımcı Metodlar
  // =====================================================

  /**
   * Risk seviyesine göre CSS sınıfı döndürür
   */
  getRiskLevelClass(riskLevel: string): string {
    switch (riskLevel?.toUpperCase()) {
      case 'HIGH':
        return 'risk-high';
      case 'MEDIUM':
        return 'risk-medium';
      case 'LOW':
        return 'risk-low';
      default:
        return 'risk-unknown';
    }
  }

  /**
   * Ödeme durumuna göre CSS sınıfı döndürür
   */
  getPaymentStatusClass(paymentStatus: string): string {
    switch (paymentStatus?.toUpperCase()) {
      case 'COMPLETED':
        return 'payment-completed';
      case 'PENDING':
        return 'payment-pending';
      case 'FAILED':
        return 'payment-failed';
      case 'EXPIRING_SOON':
        return 'payment-expiring';
      case 'WARNING':
        return 'payment-warning';
      default:
        return 'payment-active';
    }
  }

  /**
   * Kalan gün sayısına göre renk sınıfı döndürür
   */
  getRemainingDaysClass(remainingDays: number): string {
    if (remainingDays <= 3) {
      return 'text-danger';
    } else if (remainingDays <= 10) {
      return 'text-warning';
    } else {
      return 'text-success';
    }
  }

  /**
   * Branş adını formatlar
   */
  formatBranchName(branch: string): string {
    if (!branch) return '';
    return branch.charAt(0).toUpperCase() + branch.slice(1).toLowerCase();
  }

  /**
   * Fiyatı formatlar
   */
  formatPrice(price: number): string {
    if (!price) return '0₺';
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  }

  /**
   * Tarihi formatlar
   */
  formatDate(date: string | Date): string {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('tr-TR');
  }

  /**
   * Yenileme stratejisinin açıklamasını döndürür
   */
  getRenewalStrategyDescription(strategy: string): string {
    switch (strategy?.toUpperCase()) {
      case 'EXTEND':
        return 'Mevcut üyelik uzatılacak';
      case 'NEW':
        return 'Yeni üyelik oluşturulacak';
      case 'UPGRADE':
        return 'Paket yükseltme yapılacak';
      default:
        return 'Bilinmeyen strateji';
    }
  }

  /**
   * Silme türünün açıklamasını döndürür
   */
  getDeletionTypeDescription(deletionType: string): string {
    switch (deletionType?.toUpperCase()) {
      case 'SINGLE':
        return 'Tek üyelik silinecek';
      case 'MULTIPLE':
        return 'Seçilen üyelikler silinecek';
      case 'ALL':
        return 'Tüm üyelikler silinecek';
      default:
        return 'Bilinmeyen silme türü';
    }
  }
}
