﻿﻿﻿﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    // Entities/DTOs/MemberFilter.cs
    // DEPRECATED: Bu DTO artık MultiBranchMemberFilterDto ile değiştirildi
    // Geriye uyumluluk için korunuyor
    public class MemberFilter : IDto
    {
        public int MemberID { get; set; }
        public int MembershipID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public string Branch { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public DateTime StartDate{ get; set; }
        public DateTime EndDate{ get; set; }
        public DateTime? UpdatedDate { get; set; } // Üyeliğin güncellenme tarihi
        public bool IsFutureStartDate { get; set; } // Üyeliğin başlangıç tarihi gelecekte mi

        // Çoklu branş sistemi için eklenen yeni alanlar
        public int MembershipTypeID { get; set; } // Paket türü ID'si
        public string TypeName { get; set; } // Paket türü adı
        public decimal Balance { get; set; } // Üye bakiyesi
        public bool IsFrozen { get; set; } // Üyelik donmuş mu?

        /// <summary>
        /// Bu üyenin diğer aktif üyelikleri (çoklu branş desteği için)
        /// </summary>
        public List<OtherActiveMembershipDto> OtherActiveMemberships { get; set; } = new List<OtherActiveMembershipDto>();

        /// <summary>
        /// Toplam aktif üyelik sayısı
        /// </summary>
        public int TotalActiveMemberships => 1 + (OtherActiveMemberships?.Count ?? 0);

        /// <summary>
        /// Tüm branşlardaki toplam kalan gün sayısı
        /// </summary>
        public int TotalRemainingDaysAllBranches => RemainingDays + (OtherActiveMemberships?.Sum(x => x.RemainingDays) ?? 0);
    }

    /// <summary>
    /// Üyenin diğer aktif üyelikleri için DTO
    /// </summary>
    public class OtherActiveMembershipDto : IDto
    {
        public int MembershipID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public int RemainingDays { get; set; }
        public DateTime EndDate { get; set; }
    }
}
