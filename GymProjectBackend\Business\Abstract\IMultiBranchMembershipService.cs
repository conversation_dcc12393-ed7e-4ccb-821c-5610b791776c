using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace Business.Abstract
{
    /// <summary>
    /// Çoklu branş üyelik yönetimi için service interface
    /// Akıllı yenileme, güvenli silme ve iki seviyeli filtreleme işlemlerini yönetir
    /// </summary>
    public interface IMultiBranchMembershipService
    {
        #region Çoklu Branş Filtreleme İşlemleri
        
        /// <summary>
        /// Çoklu branş desteği ile üye listesini getirir
        /// Her üye için tüm aktif üyelikleri ayrı ayrı gösterir
        /// </summary>
        IDataResult<List<MultiBranchMemberFilterDto>> GetMultiBranchMemberDetails(
            string searchText = null, 
            int? gender = null, 
            string branchFilter = null, 
            int pageNumber = 1, 
            int pageSize = 50);

        /// <summary>
        /// Branş bazlı özet bilgileri getirir (İki seviyeli filtreleme - Seviye 1)
        /// </summary>
        IDataResult<List<BranchSummaryDto>> GetBranchSummary();

        /// <summary>
        /// Belirli bir branştaki paket türlerini ve üye sayılarını getirir (İki seviyeli filtreleme - Seviye 2)
        /// </summary>
        IDataResult<List<PackageTypeCountDto>> GetPackageTypesByBranch(string branch);

        /// <summary>
        /// Üyenin tüm aktif üyeliklerini getirir (silme işlemi için)
        /// </summary>
        IDataResult<MemberActiveMembershipsDto> GetMemberActiveMemberships(int memberID);

        #endregion

        #region Akıllı Üyelik Yenileme İşlemleri

        /// <summary>
        /// Üyelik yenileme analizi yapar
        /// Sistem otomatik olarak yenileme mi yoksa yeni üyelik mi oluşturacağını belirler
        /// </summary>
        IDataResult<MembershipRenewalAnalysisDto> AnalyzeMembershipRenewal(int memberID, int membershipTypeID);

        /// <summary>
        /// Akıllı üyelik yenileme işlemini gerçekleştirir
        /// </summary>
        IResult ProcessSmartMembershipRenewal(SmartMembershipRenewalDto renewalDto);

        /// <summary>
        /// Üyelik yenileme stratejisini belirler
        /// EXTEND: Mevcut üyeliği uzat (aynı branş + aynı paket)
        /// NEW: Yeni üyelik oluştur (farklı branş veya farklı paket)
        /// UPGRADE: Paket yükseltme (aynı branş, farklı paket)
        /// </summary>
        IDataResult<string> DetermineRenewalStrategy(int memberID, int membershipTypeID);

        #endregion

        #region Akıllı Silme İşlemleri

        /// <summary>
        /// Silme işlemi öncesi analiz yapar
        /// Hangi üyeliklerin silinebileceğini ve risklerini belirler
        /// </summary>
        IDataResult<MembershipDeletionAnalysisDto> AnalyzeMembershipDeletion(int memberID);

        /// <summary>
        /// Akıllı silme işlemini gerçekleştirir
        /// Çoklu üyelikleri güvenli şekilde yönetir
        /// </summary>
        IResult ProcessSmartMembershipDeletion(SmartMembershipDeletionRequestDto deletionRequest);

        /// <summary>
        /// Tek üyelik silme işlemi (geriye uyumluluk için)
        /// </summary>
        IResult DeleteSingleMembership(int membershipID, string deletionReason = null);

        /// <summary>
        /// Üyenin tüm üyeliklerini siler
        /// </summary>
        IResult DeleteAllMemberships(int memberID, string deletionReason = null);

        #endregion

        #region Yardımcı İşlemler

        /// <summary>
        /// Üyenin mevcut durumunu getirir
        /// </summary>
        IDataResult<MemberCurrentStatusDto> GetMemberCurrentStatus(int memberID);

        /// <summary>
        /// Branş bazlı istatistikleri getirir
        /// </summary>
        IDataResult<Dictionary<string, object>> GetBranchStatistics();

        /// <summary>
        /// Üyelik çakışma kontrolü yapar
        /// Aynı branş ve paket türünde aktif üyelik var mı kontrol eder
        /// </summary>
        IDataResult<bool> CheckMembershipConflict(int memberID, int membershipTypeID);

        /// <summary>
        /// Üyelik geçmişini getirir (analiz için)
        /// </summary>
        IDataResult<List<object>> GetMembershipHistory(int memberID, int? branchFilter = null);

        #endregion

        #region Performans ve Cache İşlemleri

        /// <summary>
        /// Cache'i temizler
        /// </summary>
        IResult ClearCache();

        /// <summary>
        /// Belirli üye için cache'i temizler
        /// </summary>
        IResult ClearMemberCache(int memberID);

        /// <summary>
        /// Sistem performans metriklerini getirir
        /// </summary>
        IDataResult<Dictionary<string, object>> GetPerformanceMetrics();

        #endregion
    }
}
