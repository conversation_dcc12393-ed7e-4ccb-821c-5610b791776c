using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    /// <summary>
    /// Akıllı üyelik silme sistemi için DTO
    /// Çoklu üyelikleri güvenli şekilde yönetir
    /// </summary>
    public class SmartMembershipDeletionRequestDto : IDto
    {
        public int MemberID { get; set; }
        
        /// <summary>
        /// Silinecek üyelik ID'leri
        /// Boş ise tüm aktif üyelikler silinir
        /// </summary>
        public List<int> MembershipIDsToDelete { get; set; } = new List<int>();
        
        /// <summary>
        /// Silme türü
        /// SINGLE: Tek üyelik sil
        /// MULTIPLE: Seçilen üyelikleri sil
        /// ALL: Tüm üyelikleri sil
        /// </summary>
        public string DeletionType { get; set; }
        
        /// <summary>
        /// Kullanıcı onayı alındı mı?
        /// </summary>
        public bool UserConfirmed { get; set; }
        
        /// <summary>
        /// Silme nedeni (opsiyonel)
        /// </summary>
        public string DeletionReason { get; set; }
    }

    /// <summary>
    /// Silme işlemi öncesi analiz sonucu
    /// Kullanıcıya hangi seçeneklerin sunulacağını belirler
    /// </summary>
    public class MembershipDeletionAnalysisDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        
        /// <summary>
        /// Silinebilir aktif üyelikler
        /// </summary>
        public List<DeletableMembershipDto> DeletableMemberships { get; set; } = new List<DeletableMembershipDto>();
        
        /// <summary>
        /// Toplam aktif üyelik sayısı
        /// </summary>
        public int TotalActiveMemberships => DeletableMemberships?.Count ?? 0;
        
        /// <summary>
        /// Toplam risk tutarı (tüm üyelikler silinirse kaybedilecek tutar)
        /// </summary>
        public decimal TotalRiskAmount => DeletableMemberships?.Sum(x => x.RiskAmount) ?? 0;
        
        /// <summary>
        /// Önerilen silme stratejisi
        /// </summary>
        public RecommendedDeletionStrategyDto RecommendedStrategy { get; set; }
        
        /// <summary>
        /// Uyarı mesajları
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Silinebilir üyelik detayı
    /// </summary>
    public class DeletableMembershipDto : IDto
    {
        public int MembershipID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        
        /// <summary>
        /// Son ödeme bilgileri
        /// </summary>
        public decimal LastPaymentAmount { get; set; }
        public DateTime LastPaymentDate { get; set; }
        public string PaymentStatus { get; set; }
        
        /// <summary>
        /// Risk seviyesi (HIGH, MEDIUM, LOW)
        /// </summary>
        public string RiskLevel { get; set; }
        
        /// <summary>
        /// Risk tutarı (silinirse kaybedilecek tutar)
        /// </summary>
        public decimal RiskAmount { get; set; }
        
        /// <summary>
        /// Risk açıklaması
        /// </summary>
        public string RiskDescription { get; set; }
        
        /// <summary>
        /// Bu üyelik silinebilir mi?
        /// </summary>
        public bool CanBeDeleted { get; set; }
        
        /// <summary>
        /// Silinememesinin nedeni (eğer CanBeDeleted = false ise)
        /// </summary>
        public string DeletionBlockReason { get; set; }
        
        /// <summary>
        /// Önerilen işlem (DELETE, FREEZE, TRANSFER)
        /// </summary>
        public string RecommendedAction { get; set; }
        
        /// <summary>
        /// Önerilen işlem açıklaması
        /// </summary>
        public string RecommendedActionDescription { get; set; }
    }

    /// <summary>
    /// Önerilen silme stratejisi
    /// </summary>
    public class RecommendedDeletionStrategyDto : IDto
    {
        /// <summary>
        /// Strateji türü
        /// SELECTIVE: Seçici silme (bazı üyelikleri koru)
        /// COMPLETE: Tam silme (tüm üyelikleri sil)
        /// FREEZE: Silme yerine dondurma öner
        /// TRANSFER: Başka pakete transfer öner
        /// </summary>
        public string StrategyType { get; set; }
        
        public string StrategyDescription { get; set; }
        public string Reasoning { get; set; }
        
        /// <summary>
        /// Bu strateji ile silinecek üyelik ID'leri
        /// </summary>
        public List<int> MembershipIDsToDelete { get; set; } = new List<int>();
        
        /// <summary>
        /// Bu strateji ile korunacak üyelik ID'leri
        /// </summary>
        public List<int> MembershipIDsToKeep { get; set; } = new List<int>();
        
        /// <summary>
        /// Tahmini finansal etki
        /// </summary>
        public decimal EstimatedFinancialImpact { get; set; }
        
        /// <summary>
        /// Müşteri memnuniyeti riski (1-10 arası)
        /// </summary>
        public int CustomerSatisfactionRisk { get; set; }
    }

    /// <summary>
    /// Silme işlemi sonucu
    /// </summary>
    public class MembershipDeletionResultDto : IDto
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        
        /// <summary>
        /// Başarıyla silinen üyelik ID'leri
        /// </summary>
        public List<int> DeletedMembershipIDs { get; set; } = new List<int>();
        
        /// <summary>
        /// Silinemeden üyelik ID'leri ve nedenleri
        /// </summary>
        public Dictionary<int, string> FailedDeletions { get; set; } = new Dictionary<int, string>();
        
        /// <summary>
        /// Silme işlemi sonrası üyenin durumu
        /// </summary>
        public PostDeletionMemberStatusDto PostDeletionStatus { get; set; }
        
        /// <summary>
        /// İşlem özeti
        /// </summary>
        public DeletionSummaryDto Summary { get; set; }
    }

    /// <summary>
    /// Silme sonrası üye durumu
    /// </summary>
    public class PostDeletionMemberStatusDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public int RemainingActiveMemberships { get; set; }
        public List<string> RemainingBranches { get; set; } = new List<string>();
        public int TotalRemainingDays { get; set; }
        public bool IsCompletelyInactive { get; set; }
        public DateTime? NextExpirationDate { get; set; }
    }

    /// <summary>
    /// Silme işlemi özeti
    /// </summary>
    public class DeletionSummaryDto : IDto
    {
        public int TotalMembershipsDeleted { get; set; }
        public decimal TotalRefundAmount { get; set; }
        public List<string> DeletedBranches { get; set; } = new List<string>();
        public DateTime DeletionDate { get; set; }
        public string DeletionReason { get; set; }
        
        /// <summary>
        /// Silme işleminin finansal etkisi
        /// </summary>
        public decimal FinancialImpact { get; set; }
        
        /// <summary>
        /// Oluşturulan iade kayıtları
        /// </summary>
        public List<int> RefundRecordIDs { get; set; } = new List<int>();
    }
}
